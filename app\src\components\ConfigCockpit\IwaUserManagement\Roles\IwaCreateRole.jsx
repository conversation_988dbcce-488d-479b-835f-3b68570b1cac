import { CreateRole } from '@cw/createrole';
import { useLocation, useNavigate } from 'react-router-dom';

const IwaCreateRole = () => {
  const navigate = useNavigate();
  const reactRouterLocation = useLocation();
  const { mapGroups, mapRoleCollections } = reactRouterLocation.state || {};
  const sourceSystemDataSync = {
    mapGroups: mapGroups ?? [],
    mapRoleCollections: mapRoleCollections ?? [],
  };

  const onCreateRoleActionClick = action => {
    if (action === 'roleSummary' || action === 'home') {
      navigate('/configCockpit/userManagement/RolesSummary');
    }
  };

  return (
    <CreateRole
      sourceSystemDataSync={sourceSystemDataSync}
      onCreateRoleActionClick={onCreateRoleActionClick}
    />
  );
};

export default IwaCreateRole;
