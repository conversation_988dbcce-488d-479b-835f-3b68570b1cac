import { ViewUser } from '@cw/viewuser';
import { Stack } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useSnackbar } from "../../../../hooks/useSnackbar";

const IwaViewUsers = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
   const { showSnackbar } = useSnackbar();

  const viewUserNavigate = (action, userId, response) => {
    if (action === 'edit') {
      if (userId) {
        navigate(`/configCockpit/userManagement/EditUser/${userId}`);
      } else {
     showSnackbar( "Missing userId for edit action.", "warning");
      }
    } else if (action === 'home') {
      navigate('/configCockpit/userManagement/UsersSummary');
    }
    if (response) {
      showSnackbar(response.message, "info");
    }
  };

  return (
    <Stack>
      <ViewUser userId={userId} viewUserNavigate={viewUserNavigate} />
    </Stack>
  );
};

export default IwaViewUsers;
