import { UserSummary } from '@cw/usersummary';
import { Stack } from '@mui/material';
import { useNavigate } from 'react-router-dom';


const IwaUsersSummary = () => {
  const navigate = useNavigate();
  const isVisible = {
    importUsers: true,
    viewUser: true,
    editUser: true,
    quickAdd: true,
    detailedAdd: true,
  };

  const onUserSummaryActionClick = (action, userId) => {
    switch (action) {
      case 'view':
        if (userId) {
          navigate(`/configCockpit/userManagement/ViewUser/${userId}`);
        }
        break;
      case 'edit':
        if (userId) {
          navigate(`/configCockpit/userManagement/EditUser${userId}`);
        }
        break;
      case 'quickadduser':
        navigate('/configCockpit/userManagement/QuickCreateUser');
        break;
      case 'adduser':
        navigate('configCockpit/userManagement/CreateUsers');
        break;
      default:
        break;
    }
  };
   const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };

  //inside return statement

  return (
    <Stack>
      <UserSummary {...isVisible} onUserSummaryActionClick={onUserSummaryActionClick} dateTimeConfig={dateTimeConfig}/>
    </Stack>
  );
};

export default IwaUsersSummary;

// inside React function
