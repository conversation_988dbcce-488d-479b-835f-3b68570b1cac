/* eslint-disable no-console */
import React from "react";
import { useNavigate } from "react-router-dom";

import { GroupSummary } from "@cw/groupsummary";

const GroupSummaryContainer = () => {
    const navigate = useNavigate();

    const onGroupSummaryActionClick = (action, groupId) => {
        const actionMap = {
            view: () => groupId && navigate(`/groupSummary/viewGroup/${groupId}`),
            edit: () => groupId && navigate(`/groupSummary/editGroup/${groupId}`),
            addgroup: () => navigate("/groupSummary/createGroup")
        };

        const handler = actionMap[action.trim()];
        if (handler) {
            handler();
        }
    };

    const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };

    return (
        <>
            <GroupSummary onGroupSummaryActionClick={onGroupSummaryActionClick} app="IWA" dateTimeConfig={dateTimeConfig} />
        </>
    );
};

export default GroupSummaryContainer;
