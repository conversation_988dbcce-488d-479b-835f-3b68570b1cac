import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Tab, Box, Button, Checkbox, IconButton, Chip, Stack, Paper, Dialog, TextField, DialogActions, DialogTitle, Typography, DialogContent, Tooltip, Autocomplete, Grid, MenuItem, Select, TableContainer, TableHead, TableRow, TableCell, TableBody, Table, FormLabel, RadioGroup, FormControlLabel, Radio, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import DescriptionIcon from "@mui/icons-material/Description";
import { useSelector, useDispatch } from "react-redux";
import GenericTabs from "../../MasterDataCockpit/GenericTabs";
import { setMaterialRows } from "../../../app/requestDataSlice";
import BottomNav from "./BottomNav";
import { useLocation } from "react-router-dom";
import { pushMaterialDisplayData, removeMaterialRow, setDisplayPayload, setMultipleMaterialHeader, setMultipleMaterialHeaderKey, setPayload } from "../../../app/payloadSlice";
import { updatePage } from "../../../app/paginationSlice";
import CloseIcon from "@mui/icons-material/Close";
import ReusableDialog from "../../Common/ReusableDialog";
import { doAjax } from "../../Common/fetchService";
import { destination_IDM, destination_MaterialMgmt } from "../../../destinationVariables";
import AdditionalData from "../../MasterDataCockpit/AdditionalData";
import { API_CODE, BUTTON_NAME, ENABLE_STATUSES, EXCLUDED_VIEWS, HEADINGS, LOCAL_STORAGE_KEYS, MATERIAL_TABLE, MATERIAL_TYPE_DRODOWN, MATERIAL_VIEWS, REGION_CODE, REQUEST_STATUS, REQUEST_TYPE, TASK_NAME } from "../../../constant/enum";
import { makeStyles } from "@mui/styles";
import useLogger from "../../../hooks/useLogger";
import { setOrgData, updateAllTabsData } from "../../../app/tabsDetailsSlice";
import useMaterialFieldConfig from "@hooks/useMaterialFieldConfig";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import ExtendMaterialSearch from "./ExtendMaterialSearch";
import { END_POINTS } from "@constant/apiEndPoints";
import { transformApiResponseToReduxPayloadExtend } from "../../../functions";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import FileCopyIcon from '@mui/icons-material/FileCopy';
import DoubleArrowIcon from '@mui/icons-material/DoubleArrow';
import { setCreatePayloadCopyForChangeLog } from "@app/changeLogReducer";
import { ToastContainer } from "react-toastify";
import OrgDataCopyModal from './OrgDataCopyModal';
import { 
  addMissingViews,
  getKeysWithOnlyNumbers, 
  transformMaterialNumberKeys, 
  sortByCode,
  updateExtendWithUploadPayloadData,
  updateExtendPayloadData,
  filterButtonsBasedOnTab
} from "@helper/helper";
import { colors } from "@constant/colors";
import useValuationClass from '@hooks/useValuationClass';
import useFetchAccordianFieldsOptions from "@hooks/useFetchAccordianFieldsOptions";
import useFetchDropdownAndDispatch from "@hooks/useFetchDropdownAndDispatch";
import useCountryBasedOnPlant from "@hooks/useCountryBasedOnPlant";
import useDisplayCall from "@hooks/useDisplayCall";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import { BUTTONS_ACTION_TYPE } from "@constant/buttonPriority";
import {useExtendDynamicButton} from "@hooks/useExtendDynamicButton";

const useStyles = makeStyles(() => ({
  customTabs: {
    "& .MuiTabs-scroller": {
      overflowX: "auto !important",
      overflowY: "hidden !important",
    },
  },
}));

const EXTENSION_TYPE = {
  NOT_EXTENDED: "notExtended",
  EXTENDED: "Extended"
};

const RequestDetailsForExtend = (props) => {
  const classes = useStyles();
  const { customError } = useLogger();
  const dispatch = useDispatch();
  const { fetchMaterialFieldConfig } = useMaterialFieldConfig()
  const { getNextDisplayDataForCreate } = useDisplayCall();
  const { fetchValuationClassData } = useValuationClass();
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestType = initialPayload?.RequestType;
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const paginationData = useSelector((state) => state.paginationData);
  const singlePayloadData = useSelector((state) => state.payload);
  const storedRows = useSelector((state) => state.request.materialRows);
  const allDropDownData = useSelector((state) => state.AllDropDown?.dropDown || {});
  const allTabsData = useSelector((state) => state.tabsData.allTabsData);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const allMaterialFieldConfigDT = useSelector((state) => state.tabsData.allMaterialFieldConfigDT);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get('RequestId');
  const isrequestType = queryParams.get('RequestType');
  const [page, setPage] = useState(0);
  const [timerId, setTimerId] = useState(null);
  const [expandedAccordion, setExpandedAccordion] = useState(null);
  const fixedOption = "Basic Data";
  const [selectedViews, setSelectedViews] = useState([fixedOption]);
  const [extendedViews, setExtendedViews] = useState([]);
  const [rows, setRows] = useState(storedRows || []);
  const selectedSections = useSelector((state) => state.selectedSections.selectedSections);
  const [addButtonDisabled, setAddButtonDisabled] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [materialOptions, setMaterialOptions] = useState([]);
  const [skip, setSkip] = useState(0);
  const [inputState, setInputState] = useState({ code: "", desc: "" });
  const [isLoading, setIsLoading] = useState(false);
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();
  const [submitForApprovalDisabled, setSubmitForApprovalDisabled] = useState(true);
  const [counter, setCounter] = useState(rows.length + 1);
  const [activeTab, setActiveTab] = useState(0);
  const [rowsAdded, setRowsAdded] = useState(storedRows.length > 0);
  const [basicData, setBasicData] = useState({});
  const [dropDownData, setDropDownData] = useState({});
  const [materialNumberValidationData, setMaterialNumberValidationData] = useState([]);
  const [currentRowData, setCurrentRowData] = useState({});
  const [allViews, setAllViews] = useState([]);
  const [openViews, setOpenViews] = useState(false);
  const [rowId, setRowId] = useState("");
  const [activeViewTab, setActiveViewTab] = useState("Basic Data");
  const [openOrgData, setOpenOrgData] = useState(false);
  const [selectedMaterialID, setSelectedMaterialID] = useState(null);
  const regionBasedSalesOrgData = useSelector((state) => state.request.salesOrgDTData);
  const selectedMaterialHeaderPayload = singlePayloadData?.[selectedMaterialID]?.headerData;
  const orgRowFields = initialPayload?.Region === REGION_CODE.EUR 
    ? {
        id: 0,
        salesOrg: null,
        dc: { value: null, options: [] },
        plant: { value: null, options: [] },
        sloc: { value: null, options: [] },
        mrpProfile: null
      }
    : {
        id: 0,
        salesOrg: null,
        dc: { value: null, options: [] },
        plant: { value: null, options: [] },
        sloc: { value: null, options: [] },
        warehouse: { value: null, options: [] },
        mrpProfile: null
      };
  const [orgRow, setOrgRow] = useState([orgRowFields]);
  const [copyOrgRowArray, setCopyOrgRowArray] = useState([]);
  const [copyOrgRowFieldData, setCopyOrgRowFieldData] = useState(
    {
      "id": 1,
      "plant": { value: null, options: [] },
      "salesOrg": null,
      "dc": { value: null, options: [] },
      "sloc": { value: null, options: [] },
      "mrpProfile": null,
      "warehouse": { value: null, options: [] },
    }
  );
  const [openAddMatPopup, setOpenAddMatPopup] = useState(false);
  const [selectedMaterialRow, setSelectedMaterialRow] = useState({});
  const [alertType, setAlertType] = useState("success");
  const selectedMaterialPayload = singlePayloadData?.[selectedMaterialID]?.payloadData;

  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [missingValidationPlant, setMissingValidationPlant] = useState([])
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [wfLevels,setWfLevels] = useState([]);
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  const [openSearchMat, setOpenSearchMat] = useState(false);
  const [isCopyOrg, setIsCopyOrg] = useState(false);
  const [callGetCountryBasedonSalesOrg,setCallGetCountryBasedonSalesOrg] = useState(false);
  const [selectedMaterialNumber,setSelectedMaterialNumber] = useState("");
  const [isDropdownLoading, setIsDropdownLoading] = useState({
    "Sales Organization": false,
    "Distribution Channel": {},
    "Plant": {}, 
    "Storage Location": {}, 
    "warehouse": {},
    "Mrp Profile": false
  });
  const [openOrgCopyModal, setOpenOrgCopyModal] = useState(false);
  const [lengthOfOrgRow, setLengthOfOrgRow] = useState(0);
  const { fetchTabSpecificData } = useFetchAccordianFieldsOptions();
  const { getContryBasedOnPlant } = useCountryBasedOnPlant({
    doAjax,
    customError,
    fetchDataAndDispatch,
    destination_MaterialMgmt
  });
  
  const {extendFilteredButtons,showWfLevels} = useExtendDynamicButton(taskData,applicationConfig,destination_IDM,BUTTON_NAME);
  const requestDetailsButton = filterButtonsBasedOnTab(extendFilteredButtons,[BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_APPROVAL,BUTTONS_ACTION_TYPE.HANDLE_SAP_SYNDICATION,BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_REVIEW,BUTTONS_ACTION_TYPE.HANDLE_SUBMIT])
  const fetchOrgSpecificData = (orgData) => {
    if (!orgData || !Array.isArray(orgData)) return;
    
    orgData.forEach(row => {
      if (row.plant?.value?.code) {
        fetchTabSpecificData(row.plant?.value?.code, MATERIAL_VIEWS.PLANT);
        if (row.salesOrg?.code || row.dc?.value?.code) {
          const salesCombination = `${row.salesOrg?.code || ''}-${row.dc?.value?.code || ''}`;
          fetchTabSpecificData(salesCombination, MATERIAL_VIEWS.SALES);
        }
        if (row.warehouse?.value?.code) {
          fetchTabSpecificData(row.warehouse?.value?.code, MATERIAL_VIEWS.WAREHOUSE);
        }
        getContryBasedOnPlant(row.plant?.value?.code)
      }
    });
  };

  const filterViewsBasedOnRegion = (views) => {
    if (!views || !Array.isArray(views)) return [];
    let filteredViews = initialPayload?.Region === REGION_CODE.EUR 
      ? views?.filter(view => 
          view !== MATERIAL_VIEWS.WAREHOUSE && 
          view !== MATERIAL_VIEWS.WORKSCHEDULING && 
          view !== MATERIAL_VIEWS.WORK_SCHEDULING
        )
      : [...views];
    
    filteredViews.sort((a, b) => {
      if (a === MATERIAL_VIEWS.BASIC_DATA) return -1;
      if (b === MATERIAL_VIEWS.BASIC_DATA) return 1;
      return 0;
    });
    
    return filteredViews;
  };

  const fetchWorkflowLevels = async () => {
    try {
      const workflowLevelsDtData = await getDynamicWorkflowDT(
        requestType,
        initialPayload?.Region,
        '',
        singlePayloadData[selectedMaterialID]?.Tochildrequestheaderdata?.MaterialGroupType,
        taskData?.ATTRIBUTE_3
      );
      setWfLevels(workflowLevelsDtData);
    } catch (err) {
      customError(err)
    }
  };

  useEffect(() => {
    if (requestType && initialPayload?.Region && selectedMaterialID && taskData?.ATTRIBUTE_3) {
      fetchWorkflowLevels();
    }
  }, [requestType, initialPayload?.Region, selectedMaterialID,taskData?.ATTRIBUTE_3]);

  useEffect(() => {
    setRows(storedRows);
    setRowsAdded(storedRows?.length > 0)
    if (storedRows?.length > 0) {
      setSelectedMaterialID(storedRows?.[0]?.id);
      handleMaterialType(storedRows?.[0]?.materialType?.code || storedRows?.[0]?.materialType);
      setActiveTab(0);
      setSelectedMaterialNumber(storedRows?.[0]?.materialNumber)
      setActiveViewTab(MATERIAL_VIEWS.BASIC_DATA);
      setSelectedViews(storedRows?.[0]?.views?.length ? 
        filterViewsBasedOnRegion(storedRows?.[0]?.views) : 
        filterViewsBasedOnRegion([fixedOption]))
      const allMaterials = getKeysWithOnlyNumbers(singlePayloadData);
      const updatedMaterials = transformMaterialNumberKeys(allMaterials);
      let payloadDataForCopy = JSON.parse(JSON.stringify(updatedMaterials));
      dispatch(setCreatePayloadCopyForChangeLog(payloadDataForCopy));
      dispatch(setPayload({keyName:"selectedMaterialID",data:storedRows?.[0]?.id}))
      if(singlePayloadData?.[storedRows?.[0]?.id]?.Tochildrequestheaderdata?.ChildRequestId){
        dispatch(setPayload({ keyName: "childRequestId", data: singlePayloadData?.[storedRows?.[0]?.id]?.Tochildrequestheaderdata?.ChildRequestId }))
      } 
    }
  }, [storedRows]);

  useEffect(() => {
    if (rows?.length === 0)
      setAddButtonDisabled(false)
  }, [rows]);

  useEffect(() => {
    ["Sales Organization", "Mrp Profile"].forEach(fetchOrgLookupData);
  }, []);

  useEffect(() => {
    if(requestId){
    const reduxOrgData = selectedMaterialHeaderPayload?.orgData
    if (reduxOrgData?.length > 0 && reduxOrgData.some(row => 
      row.plant?.value?.code && 
      (row.salesOrg?.code || row.dc?.value?.code)
    )) {
      fetchOrgSpecificData(reduxOrgData);
    }
  }
  },[selectedMaterialHeaderPayload?.orgData])

  useEffect(() => {
    if (selectedViews.includes(MATERIAL_VIEWS?.SALES) && !selectedViews.includes(MATERIAL_VIEWS?.SALES_PLANT)) {
      setSelectedViews((prev) => filterViewsBasedOnRegion([...prev, "Sales-Plant"]));
    }
  }, [selectedViews]);

  const handleOkDialog = () => {
    handleDialogClose();
  };

  const getMaterialNo = (value = "", reset = false) => {
    const payload = {
      materialNo: value ?? "",
      top: 500,
      skip: reset ? 0 : skip,
      salesOrg:regionBasedSalesOrgData?.uniqueSalesOrgList?.map(item => item.code)?.join("$^$") || ""
    };
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        if (reset)
          setMaterialOptions(data?.body);
        else
          setMaterialOptions((prevOptions) => [...prevOptions, ...data?.body]);
        setIsLoading(false);
      }
    };

    const hError = () => {
      setIsLoading(false);
    };

    setIsLoading(true);

    doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_SEARCH_PARAMS_MATERIAL_NO}`, "post", hSuccess, hError, payload);
  };

  const disableCheck = !ENABLE_STATUSES.includes(props?.requestStatus);
  const getMaterialNumValidation = (materialTypeValue) => {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setMaterialNumberValidationData(data?.body);
      }
    };
    const hError = (error) => {
      console.error(error, "while fetching the validation data of material number");
    };
    doAjax(`/${destination_MaterialMgmt}/data/getNumberRangeForMaterialType?materialType=${materialTypeValue?.code}`, "get", hSuccess, hError);
  };

  function getViewsBasedOnMaterialType(materialType) {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        let filteredViews = data?.body?.filter(view => !EXCLUDED_VIEWS.includes(view));
        if (initialPayload?.Region === REGION_CODE.EUR) {
          filteredViews = filteredViews?.filter(view => 
            view !== MATERIAL_VIEWS.WAREHOUSE && 
            view !== MATERIAL_VIEWS.WORK_SCHEDULING && 
            view !== MATERIAL_VIEWS.WORKSCHEDULING
          );
        }
        
        setAllViews(filteredViews);
      }
    };
    
    const hError = (error) => {
      customError(error);
    };
    
    doAjax(`/${destination_MaterialMgmt}/data/getViewForMaterialType?materialType=${materialType}`, "get", hSuccess, hError);
  }

  useEffect(() => {
    getMaterialNo();
  }, []);

  const isExternalRangeAllowed = materialNumberValidationData?.[1]?.External === "X" ? true : false;
  const isExtWOCheckAllowed = materialNumberValidationData?.some((item) => item.ExtNAwock === "X");

  function handleMaterialType(materialType) {
    const region = initialPayload?.Region || REGION_CODE.US;
    const materialTypeExists = allMaterialFieldConfigDT.some(obj => 
      obj[region] && obj[region][materialType]
    );
    
    if (!materialTypeExists && materialType) {
      fetchMaterialFieldConfig(materialType, region);
    } else {
      if (!materialType) {
        dispatch(updateAllTabsData({}));
      } else {
        const gotMatConfigFieldFromRedux = allMaterialFieldConfigDT?.find(
          materialTypes => materialTypes?.[region] && materialTypes?.[region][materialType]
        );
        gotMatConfigFieldFromRedux && dispatch(updateAllTabsData(gotMatConfigFieldFromRedux[region][materialType]?.allfields));
      }
    }
    
    if (materialType) {
      fetchValuationClassData(materialType);
    }
  }

  const handleCellEdit = (params) => {
    const { id, field, value } = params;
    const updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    setCurrentRowData({
      ...currentRowData,
      [field]: value,
    });
    if (field === MATERIAL_TABLE.MATERIALTYPE) {
      getMaterialNumValidation(value);
      getViewsBasedOnMaterialType(value);
      setSelectedViews([fixedOption]);
      setOrgData([orgRowFields]);
      dispatch(setMultipleMaterialHeaderKey({ materialID: id, keyName: 'views', data: [fixedOption] }));
      dispatch(setMultipleMaterialHeaderKey({ materialID: id, keyName: 'orgData', data: '' }));
      handleMaterialType(value?.code);
    }
    setRows(updatedRows);
    dispatch(setMultipleMaterialHeaderKey({ materialID: id, keyName: field, data: value }));
  };

  const handleRowSelection = (params) => {
    setSelectedMaterialID(params.row.id);
    setSelectedMaterialRow(params.row)
    setSelectedMaterialNumber(params.row.materialNumber)
    setAllViews(params?.row?.views)
    handleMaterialType(params?.row?.materialType?.code || params.row?.materialType)
    setSelectedViews(Boolean(params?.row?.views) ? params.row?.views : [fixedOption]);
    setOrgRow(params?.row?.orgData?.length ? params.row?.orgData : [orgRowFields]);
    setActiveTab(0);
    setActiveViewTab("Basic Data");
  };

  const handleDialogClickOpen = () => {
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const viewsClose = (event, reason) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") {
      return;
    }
    setOpenOrgData(false);
  };
  const handleSelectAll = () => setSelectedViews(filterViewsBasedOnRegion(allViews));
  const AddCopiedMaterial = (materials) => {
    setOpenAddMatPopup(false)
    if (materials?.length) {
      let newRows = [...rows];
      materials?.forEach(mat => {
        const id = mat?.Material;
        let newRow = { ...mat };
        let payloadData = singlePayloadData?.[mat.id]?.payloadData
          ? JSON.parse(JSON.stringify(singlePayloadData?.[mat.id]?.payloadData))
          : '';

        newRow.id = id;
        newRow.globalMaterialDescription = '';
        newRow.materialNumber = '';
        newRow.included = true,
          newRow.industrySector = mat?.IndSector,
          newRow.materialType = mat?.MatlType,
          newRow.materialNumber = mat?.Material,
          newRow.globalMaterialDescription = mat?.MaterialDescrption
        newRow.views = mat?.Views ? mat?.Views.split(',').map(view => view.trim()) : [fixedOption];
        newRow.validated = false
        newRows.push(newRow);
        dispatch(setMultipleMaterialHeader({ materialID: id, data: newRow, payloadData }));
      });
      setExtendedViews((prev) => [
        ...prev,
        ...newRows.map((val) => ({ material: val?.Material, views: val?.views }))
      ]);
      setRows(newRows);
      dispatch(setMaterialRows(newRows));
      setCounter(counter + 1);
      setRowsAdded(true);
      setSubmitForApprovalDisabled(true);
    }
  }

  const columns = [
    {
      field: "included",
      headerName: "Included",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) =>{
        if (!params?.row) return null;
        return (
          <Checkbox checked={params?.row?.included} disabled={disableCheck} onChange={(e) => {
            if (params?.row?.id) {
              handleCellEdit({ 
                id: params.row.id, 
                field: "included", 
                value: e.target.checked 
              });
            }
          }}  />
        )
      }
    },
    { 
      field: "lineNumber", 
      headerName: "Line Number", 
      flex: 0.6, 
      editable: requestType === "Create" ? true : false, 
      align: "center", 
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = storedRows?.findIndex(row => row?.id === params?.row?.id);
        const lineNumber = (rowIndex + 1) * 10;
        return <div>{lineNumber}</div>;
      }
    },
    {
      field: "industrySector",
      headerName: "Industry Sector",
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["IndSector"] || []}
            value={params?.row?.industrySector}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "industrySector",
                value: newValue,
              })
            }
            placeholder="Select Industry Sector"
            minWidth="90%"
            disabled={true}
            listWidth={232}
            title={`${params.row?.industrySector?.code || ""} - ${params.row?.industrySector?.desc || ""}`}
          />
        );
      }
    },
    {
      field: "materialType",
      headerName: "Material Type",
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={MATERIAL_TYPE_DRODOWN || []}
            value={params?.row?.materialType}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "materialType",
                value: newValue,
              })
            }
            placeholder="Select Material Type"
            disabled={true}
            minWidth="90%"
            listWidth={232}
            title={`${params.row?.materialType?.code || ""} - ${params.row?.materialType?.desc || ""}`}
          />
        );
      }
    },

    {
      field: "materialNumber",
      headerName: "Material Number",
      flex: requestType === "Extend" ? 0.8 : 1,
      editable: !isExternalRangeAllowed && !isExtWOCheckAllowed ? false : true,
      align: "center",
      headerAlign: "center",
      renderHeader: () => (
        <span>
          Material Number<span style={{ color: "red" }}>*</span>
        </span>
      ),
      renderCell: (params) => {
        return (
          <>
            {initialPayload?.RequestType === REQUEST_TYPE.EXTEND ? (
              <TextField
                fullWidth
                placeholder="Enter Material Number"
                variant="outlined"
                size="small"
                name="material number"
                value={params?.row?.materialNumber}
                 sx={{
                    '& .MuiInputBase-root.Mui-disabled': {
                      '& > input': {
                        WebkitTextFillColor: colors.black.dark,
                        color: colors.black.dark,
                      },
                    }
                  }}
                onChange={(_, newValue) =>
                  handleCellEdit({
                    id: params.row.id,
                    field: "materialNumber",
                    value: newValue,
                  })
                }
                disabled={!isExternalRangeAllowed && !isExtWOCheckAllowed}
              />
            ) : (
              params?.row?.materialNumber
            )}
          </>
        );
      },
    },
    {
      field: "globalMaterialDescription",
      flex: requestType === "Extend" ? 0.8 : 1,
      headerName: "Material Description",
      renderHeader: () => (
        <span>
          Material Description<span style={{ color: "red" }}>*</span>
        </span>
      ),
      renderCell: (params) => {
        return (
          <>
            {initialPayload?.RequestType === REQUEST_TYPE.EXTEND ? <TextField
              fullWidth
              placeholder="Enter Material Description"
              variant="outlined"
              disabled={true}
              size="small"
              name="material description"
              sx={{
                '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                }
              }}
              onChange={(_, newValue) =>
                handleCellEdit({
                  id: params.row.id,
                  field: "globalMaterialDescription",
                  value: newValue,
                })
              }
              value={params?.row?.globalMaterialDescription}
            /> : (
              params?.row?.globalMaterialDescription
            )}

          </>
        );
      },
      align: "center",
      headerAlign: "center",
      editable: true,
    },

    {
          field: "views",
          headerName: "",
          flex: requestType === "Extend" ? 1.5 : 1,
          align: "center",
          headerAlign: "center",
          renderCell: (params) => {
            return (
              <Stack direction="row" spacing={0} alignItems="center">
                <Button
                  variant="contained"
                  size="small"
                  sx={{ minWidth: 80 }}
                  onClick={() => {
                    getViewsBasedOnMaterialType(params.row.materialType);
                    // setExtendedViews(params?.row?.views);
                    setOpenViews(true);
                    setRowId(params.row.id);
                    setSelectedMaterialRow(params.row)
                    setSelectedViews(Boolean(params?.row?.Views) ? params?.row?.Views : [fixedOption]);
                  }}
                >
                  Views
                </Button>
                <DoubleArrowIcon color="disabled" fontSize="small" sx={{ mx: 0.5 }} />
                <Button
                  variant="contained"
                  size="small"
                  sx={{ minWidth: 100 }}
                  onClick={() => {
                    setOpenOrgData(true);
                    setRowId(params.row.id);
                    setOrgRow(params?.row?.orgData?.length ? params.row?.orgData : [orgRowFields]);
                      setSelectedMaterialRow(params.row)
                      getSalesOrg(params?.row?.materialNumber, EXTENSION_TYPE.NOT_EXTENDED);
                      setIsCopyOrg(false);
                    
                  }}
                >
                  ORG Data
                </Button>
                <DoubleArrowIcon color="disabled" fontSize="small" sx={{ mx: 0.5 }} />
                  <Tooltip title="Click after changing Views or ORG Data">
                    <IconButton
                      onClick={() => {
                        setOpenOrgData(true);
                        setRowId(params.row.id);
                        setIsCopyOrg(true);
                        setSelectedMaterialRow(params.row)
                          getSalesOrg(params?.row?.materialNumber, EXTENSION_TYPE.EXTENDED);
                          setCopyOrgRowFieldData(
                            copyOrgRowArray.find((ele) => ele.id === params.row?.id) || {
                              id: params.row?.id,
                              plant: null,
                              salesOrg: null,
                              dc: {
                                value: null,
                                options: []
                              },
                              sloc: {
                                value: null,
                                options: []
                              },
                              mrpProfile: null,
                              warehouse: null,
                            }
                          );
                      }}
                      disabled={disableCheck}
                      color="primary"
                      size="small"
                    >
                      <FileCopyIcon />
                    </IconButton>
                  </Tooltip>
              </Stack>

            )
          },
        
        
    },
    {
      field: "action",
      headerName: "Action",
      flex: 0.9,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <Stack direction="row" alignItems="center" sx={{ marginLeft: "0.5rem", magrinRight: "0.5rem" }} spacing={0.5}>
            {!requestId && (
              <Tooltip title="Delete Row">
                <IconButton
                  onClick={() => {
                    setRows(rows.filter((row) => row.id !== params.row.id));
                    dispatch(removeMaterialRow(params.row.id));
                    dispatch(setMaterialRows(rows.filter((row) => row.id !== params.row.id)))
                    if (!rows?.length)
                      setAddButtonDisabled(false)
                  }}
                  color="error"
                >
                  <DeleteOutlineOutlinedIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        );
      },
    },
  ];
 
  const getSalesOrg = (materialNo, type) => {
    setIsDropdownLoading(prev => ({ ...prev, "Sales Organization": true }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let salesOrgData;
        if (type === EXTENSION_TYPE.NOT_EXTENDED) {
          salesOrgData = sortByCode(data.body);
        } else {
          salesOrgData = data.body.length > 0 ? sortByCode(data.body) : [];
        }
        setDropDownData((prev) => ({ ...prev, ['Sales Organization']: salesOrgData }));
      }
      setIsDropdownLoading(prev => ({ ...prev, "Sales Organization": false }));
    }
    
    const hError = () => { 
      setIsDropdownLoading(prev => ({ ...prev, "Sales Organization": false }));
    }
    
    doAjax(`/${destination_MaterialMgmt}/data/${type === EXTENSION_TYPE.NOT_EXTENDED ? 'getSalesOrgNotExtended' : 'getSalesOrgExtended'}?materialNo=${materialNo}&region=${initialPayload?.Region}`, "get", hSuccess, hError);
  }
  const getPlant = (materialNo, type, salesOrg,index) => {
    setIsDropdownLoading(prev => ({ 
      ...prev, 
      "Plant": { ...prev["Plant"], [index]: true } 
    }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let plantData;
        if (type === EXTENSION_TYPE.NOT_EXTENDED) {
          plantData = sortByCode(data.body);
        } else {
          plantData = data.body.length > 0 ? sortByCode(data.body || []) : [];
        }
        
        setDropDownData((prev) => ({ ...prev, ['Plant']: plantData }));
      }
      setIsDropdownLoading(prev => ({ 
        ...prev, 
        "Plant": { ...prev["Plant"], [index]: false } 
      }));
    }
    
    const hError = () => { 
      setIsDropdownLoading(prev => ({ 
        ...prev, 
        "Plant": { ...prev["Plant"], [index]: false } 
      }));
    }
    
    const salesOrgParam = salesOrg ? `&salesOrg=${salesOrg.code}` : '';
    doAjax(`/${destination_MaterialMgmt}/data/${type === EXTENSION_TYPE.NOT_EXTENDED ? 'getPlantNotExtended' : 'getPlantExtended'}?materialNo=${materialNo}&region=${initialPayload?.Region}${salesOrgParam}`, "get", hSuccess, hError);
  }
  const getWarehouse = (materialNo, type, plant, index) => {
    setIsDropdownLoading(prev => ({ 
      ...prev, 
      "warehouse": { ...prev["warehouse"], [index]: true } 
    }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let warehouseData;
        if (type === EXTENSION_TYPE.NOT_EXTENDED) {
          warehouseData = sortByCode(data.body);
        } else {
          warehouseData = data.body.length > 0 ? sortByCode(data.body || []): []
        }
        
        setDropDownData((prev) => ({ ...prev, ['warehouse']: warehouseData }));
      }
      setIsDropdownLoading(prev => ({ 
        ...prev, 
        "warehouse": { ...prev["warehouse"], [index]: false } 
      }));
    }
    
    const hError = () => { 
      setIsDropdownLoading(prev => ({ 
        ...prev, 
        "warehouse": { ...prev["warehouse"], [index]: false } 
      }));
    }
    
    const plantParam = plant ? `&plant=${plant.code}` : '';
    doAjax(`/${destination_MaterialMgmt}/data/${type === EXTENSION_TYPE.NOT_EXTENDED ? 'getWarehouseNotExtended' : 'getWarehouseExtended'}?materialNo=${materialNo}&region=${initialPayload?.Region}${plantParam}`, "get", hSuccess, hError);
  }
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setActiveViewTab(event?.target?.id === "AdditionalKey" ? "Additional Data" : selectedViews?.filter((views) => views !== MATERIAL_VIEWS.STORAGE)?.[newValue]);
  };

  const fetchOrgLookupData = (field) => {
    setIsDropdownLoading(prev => ({ ...prev, [field]: true }));
    
    const endpoints = {
      "Sales Organization": "/getSalesOrg",
      "Mrp Profile": "/getMRPProfile",
    };
    
    const successHandler = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        const sortedData = sortByCode(data.body);
        setDropDownData((prev) => ({ ...prev, [field]: sortedData }));
      }
      setIsDropdownLoading(prev => ({ ...prev, [field]: false }));
    };
    
    const errorHandler = (error) => {
      console.error(error);
      setIsDropdownLoading(prev => ({ ...prev, [field]: false }));
    };

    doAjax(`/${destination_MaterialMgmt}/data${endpoints[field]}`, "get", successHandler, errorHandler);
  };
  const addOtherViews = (plantIds) => {
    addMissingViews(
      plantIds, 
      selectedViews, 
      selectedMaterialPayload, 
      selectedMaterialID, 
      orgRow, 
      dispatch, 
      pushMaterialDisplayData
    );
  };
  const handleAccordionChange = (accordionIndex, accData, view) => (event, isExpanded) => {
    let payload = {};
    let url = "";
    let plantData = "";

    if (view === "Purchasing" || view === "Costing") {
      payload = {
        materialNo: accData?.Material,
        plant: accData?.Plant
      }
      plantData = accData?.Plant
      url = `/${destination_MaterialMgmt}/data/displayLimitedPlantData`;
    }
    else if (view === "Accounting") {
      payload = {
        materialNo: accData?.Material,
        valArea: accData?.ValArea
      }
      plantData = accData?.ValArea
      url = `/${destination_MaterialMgmt}/data/displayLimitedAccountingData`;
    }
    else if (view === "Sales") {
      payload = {
        materialNo: accData?.Material,
        salesOrg: accData?.SalesOrg,
        distChnl: accData?.DistrChan,
      }
      plantData = `${accData?.SalesOrg}-${accData?.DistrChan}`
      url = `/${destination_MaterialMgmt}/data/displayLimitedSalesData`;
    }

    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        if (view === "Purchasing" || view === "Costing") {
          dispatch(pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: accData?.Plant,
            data: data?.body?.SpecificPlantDataViewDto[0]
          }));
        }
        else if (view === "Accounting") {
          dispatch(pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: accData?.ValArea,
            data: data?.body?.SpecificAccountingDataViewDto[0]
          }));
        }
        else if (view === "Sales") {
          dispatch(pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: `${accData?.SalesOrg}-${accData?.DistrChan}`,
            data: data?.body?.SpecificSalesDataViewDto[0]
          }));
        }
      }
    }

    const hError = () => { }
    const shouldCallApi =
      !singlePayloadData?.[selectedMaterialID]?.payloadData?.[view]?.[plantData];

    if (shouldCallApi) {
      doAjax(url, "post", hSuccess, hError, payload)
    }

    setExpandedAccordion(isExpanded ? accordionIndex : null);
  };

  const renderTabContent = () => {
    if (allTabsData && activeViewTab && (allTabsData[activeViewTab] || activeViewTab === "Additional Data")) {
      if (activeViewTab === "Additional Data") {
        return [<AdditionalData disabled={disableCheck} materialID={selectedMaterialID} selectedMaterialNumber={selectedMaterialNumber}/>];
      }
      return [
        <GenericTabs
          disabled={disableCheck}
          materialID={selectedMaterialID}
          basicData={basicData}
          setBasicData={setBasicData}
          dropDownData={dropDownData}
          allTabsData={allTabsData}
          basicDataTabDetails={allTabsData[activeViewTab]}
          activeViewTab={activeViewTab}
          selectedViews={selectedViews}
          handleAccordionClick={handleAccordionChange}
          missingValidationPlant={missingValidationPlant}
          selectedMaterialNumber={selectedMaterialNumber}
          callGetCountryBasedonSalesOrg={callGetCountryBasedonSalesOrg}
        />];
    } else {
      return <></>;
    }
  };

  const handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    setInputState({ code: inputValue, desc: "" });
    setSkip(0)
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getMaterialNo(inputValue, true);
    }, 500);

    setTimerId(newTimerId);
  };

  useEffect(() => {
    if (skip > 0) {
      getMaterialNo(inputState?.code);
    }
  }, [skip]);


  const handleSalesOrgSel = (fieldName, selectedValue, index) => {
    if (fieldName === "Sales Organization") {
      salesOrgDependentOrgElement(selectedValue, index);
      const materialNumber = rows?.find(row => row.id === rowId)?.materialNumber;
      getPlant(materialNumber, isCopyOrg ? EXTENSION_TYPE.EXTENDED : EXTENSION_TYPE.NOT_EXTENDED, selectedValue,index);
    }
  };
  const salesOrgDependentOrgElement = (salesOrg, index, org = '', row = '') => {
    setIsDropdownLoading(prev => ({ 
      ...prev, 
      "Distribution Channel": { ...prev["Distribution Channel"], [index]: true } 
    }));
    
    return new Promise((resolve, reject) => {
      const hSuccess = (data) => {
        if(data.statusCode === API_CODE.STATUS_200){
          const sortedData = sortByCode(data.body);
          let rowOption = org ? JSON.parse(JSON.stringify(org)) : JSON.parse(JSON.stringify(orgRow));
          if (!isCopyOrg) {
            rowOption[index].salesOrg = salesOrg;
            rowOption[index].dc.options = sortedData;
            setOrgRow(rowOption);
          } else {
            setCopyOrgRowFieldData((prev) => ({ 
              ...prev, 
              salesOrg: salesOrg, 
              dc: { 
                value: null, 
                options: sortedData?.length > 0 ? sortedData : [] 
              } 
            }))
          }
        }
        setIsDropdownLoading(prev => ({ 
          ...prev, 
          "Distribution Channel": { ...prev["Distribution Channel"], [index]: false } 
        }));
        
        resolve(data);
      };

      const hError = (error) => {
        setIsDropdownLoading(prev => ({ 
          ...prev, 
          "Distribution Channel": { ...prev["Distribution Channel"], [index]: false } 
        }));
        reject(error);
      };
      
      let matNo = rows?.find(row => row.id === rowId)?.materialNumber;
      if (matNo) doAjax(`/${destination_MaterialMgmt}/data/${isCopyOrg ? 'getDistributionChannelExtended' : 'getDistributionChannelNotExtended'}?materialNo=${matNo}` + `&salesOrg=${salesOrg?.code}`, "get", hSuccess, hError);
    })
  };
  const handlePlant = (newValue, index) => {
    setSlocOption(newValue, index);
    const materialNumber = rows?.find(row => row.id === rowId)?.materialNumber;
    getWarehouse(materialNumber, isCopyOrg ? EXTENSION_TYPE.EXTENDED : EXTENSION_TYPE.NOT_EXTENDED, newValue, index);
  };
  const setSlocOption = (newvalue, index, org = '', material) => {
    setIsDropdownLoading(prev => ({ 
      ...prev, 
      "Storage Location": { ...prev["Storage Location"], [index]: true }
    }));
    
    const hSuccess = (data) => {
      setIsDropdownLoading(prev => ({ 
        ...prev, 
        "Storage Location": { ...prev["Storage Location"], [index]: false }
      }));
      
      if(data.statusCode === API_CODE.STATUS_200){
        const sortedData = sortByCode(data.body);
        let rowOption = org ? JSON.parse(JSON.stringify(org)) : JSON.parse(JSON.stringify(orgRow));
        if (!isCopyOrg) {
          rowOption[index].plant.value = newvalue;
          rowOption[index].sloc.options = sortedData;
          setOrgRow(rowOption);
        } else {
          setCopyOrgRowFieldData((prev) => ({ 
            ...prev, 
            plant: { value: newvalue, options: [] }, 
            sloc: { value: null, options: sortedData?.length > 0 ? sortedData : [] } 
          }));
        }
      }
      if (material) {
        dispatch(setMultipleMaterialHeaderKey({ materialID: material?.id, keyName: 'orgData', data: rowOption }));
        let matRow = rows?.length || [JSON.parse(JSON.stringify(material))];
        let matIndex = matRow.findIndex(ele => ele.id === material?.id);
        matRow[matIndex].orgData = rowOption;
        dispatch(setMaterialRows(matRow));
      }
    };
    
    const hError = (error) => {
      console.error(error);
      setIsDropdownLoading(prev => ({ 
        ...prev,
        "Storage Location": { ...prev["Storage Location"], [index]: false }
      }));
    };
    
    let matNo = rows.find(row => row.id === rowId)?.materialNumber;
    const currentRow = orgRow[index];
    const salesOrgParam = currentRow?.salesOrg ? `&salesOrg=${currentRow.salesOrg.code}` : '';
    
    if (matNo) doAjax(`/${destination_MaterialMgmt}/data/${isCopyOrg ? 'getStorageLocationExtended' : 'getStorageLocationNotExtended'}?materialNo=${matNo}&region=${initialPayload?.Region}&plant=${newvalue?.code}${salesOrgParam}`, "get", hSuccess, hError);
  };
  const setDc = (newValue, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].dc.value = newValue;
    setOrgRow(org);
  };

  const removeOrgRow = (index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org.splice(index, 1);
    setOrgRow(org);
  };

  const setSloc = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].sloc.value = value;
    setOrgRow(org);
  };
  const setWarehouse = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].warehouse.value = value;
    setOrgRow(org);
  };
  const setMrpDefault = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].mrpProfile = value;
    setOrgRow(org);
  }
  const AddAnotherOrg = () => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org.push({ id: 1, plant: { value: null, options: [] }, salesOrg: null, dc: { value: null, options: [] }, sloc: { value: null, options: [] }, warehouse: { value: null, options: [] }, mrpProfile: null });
    setOrgRow(org);
  };

  const getView = (val, orgRow, initialPayload, selectedViews,) => {
    const payload = {
      "material": selectedMaterialRow?.materialNumber,
      "wareHouseNumber": val?.warehouse?.value?.code ?? "",
      "plant":val?.plant?.value?.code ?? "",
      "salesOrg": val?.salesOrg?.code ?? "",
      "storageLocation": val?.sloc?.value?.code ?? "",
      "distributionChannel": val?.dc?.value?.code ?? "",
      "valArea":val?.plant?.value?.code ?? ""
    };
    const hSuccess = (data) => {
      const transformedPayload = transformApiResponseToReduxPayloadExtend(data?.body, orgRow, initialPayload, selectedViews, selectedMaterialRow);
      const updatedPayload = requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD || isrequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD  ? updateExtendWithUploadPayloadData(singlePayloadData, transformedPayload) : updateExtendPayloadData(singlePayloadData, transformedPayload);
      dispatch(setDisplayPayload({ data: updatedPayload }));
      setCallGetCountryBasedonSalesOrg(!callGetCountryBasedonSalesOrg)
    };
    const hError = (error) => {
      customError(error)
    };
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`, "post", hSuccess, hError, payload);
  }

  const copyOrgRowField = [
    {
      "id": 1,
      "plant": {
        "value": null,
        "options": []
      },
      "salesOrg": null,
      "dc": {
        "value": null,
        "options": []
      },
      "sloc": {
        "value": null,
        "options": []
      },
      "warehouse": { "value": null, "options": [] },
      "mrpProfile": null
    }
  ]

  const handlePageChange = (newPage) => {
    dispatch(updatePage(newPage));
    setPage(newPage);
  };

  useEffect(() => {
    if (paginationData?.page !== 0 && (requestType === REQUEST_TYPE?.EXTEND_WITH_UPLOAD || requestType === REQUEST_TYPE?.EXTEND)) {
      getNextDisplayDataForCreate();
    }
    setPage(paginationData?.page || 0);
  }, [paginationData?.page]);

  const fieldData = !isCopyOrg ? orgRow : copyOrgRowField
  return (
    <div>
      <div style={{ padding: "0", width: "100%", margin: "0" }}>
        <div style={{ height: 300, width: "100%" }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              marginBottom: "10px",
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                setOpenSearchMat(true);
              }}
              disabled={addButtonDisabled || disableCheck || (requestId && props?.requestStatus !== REQUEST_STATUS.DRAFT)}
            >
              + Add
            </Button>
          </Box>
          {rows && rows?.length > 0 ? (
            <>
            <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
              <div style={{ height: "100%"}}>
              <DataGrid
                rows={rows}
                columns={columns}
                autoHeight={false}
                pageSize={50}
                page={page}
                rowsPerPageOptions={[50]}
                rowCount={paginationData?.totalElements || 0}
                onRowClick={handleRowSelection}
                onCellEditCommit={handleCellEdit}
                onPageChange={(newPage) => handlePageChange(newPage)}
                disableSelectionOnClick
                getRowClassName={(params) =>
                  params.id === selectedMaterialID ? "selected-row" : ""
                }
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height:  `${Math.min(rows.length * 50 + 130, 300)}px`,
                      overflow: "auto"
                }}
                sx={{
                  "& .selected-row": {
                    backgroundColor: "rgb(234 233 255)",
                  }
                }}
              />
              </div>
              </div>
            </>
          ) : (
            <>
            <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
              <div style={{ height: "100%"}}>              
              <DataGrid
                rows={rows}
                autoHeight={false}
                columns={columns}
                pageSize={5}
                rowsPerPageOptions={[5]}
                page={page}
                onRowClick={handleRowSelection}
                onCellEditCommit={handleCellEdit}
                onPageChange={(newPage) => handlePageChange(newPage)}
                disableSelectionOnClick
                getRowClassName={(params) =>
                  params.id === selectedMaterialID ? "selected-row" : ""
                }
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: `${Math.min(rows.length * 50 + 130, 300)}px`,
                      overflow: "auto"
                }}
                sx={{
                  "& .selected-row": {
                    backgroundColor: "rgb(234 233 255)",
                  }
                }}
              />
            </div>
            </div>
            </>
          )}

        </div>
      </div>
      {
          selectedMaterialID && rowsAdded && rows?.length > 0 && selectedSections.length > 0 && allTabsData && Object.getOwnPropertyNames(allTabsData)?.length > 0 && (
            <Box sx={{ marginTop: "45px" }}>
              <Tabs 
               sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
               value={activeTab} onChange={handleTabChange} className={classes.customTabs} aria-label="material tabs">
                {selectedViews && orgRow.length > 0 && selectedViews?.length > 0 ? selectedViews?.filter((views) => views !== MATERIAL_VIEWS.STORAGE)?.map((view, index) => <Tab key={index} label={view} />) : <></>}
                <Tab label="Additional Data" key="Additional data" id="AdditionalKey" />
              </Tabs>
              {rows?.length > 0 && <Box sx={{ padding: 2, marginTop: 2 }}>{renderTabContent()}</Box>}
              {<BottomNav 
                  activeTab={activeTab} 
                  submitForApprovalDisabled={submitForApprovalDisabled} 
                  filteredButtons={requestDetailsButton} 
                  workFlowLevels={wfLevels}
                  showWfLevels = {showWfLevels}
                  childRequestHeaderData={singlePayloadData?.[selectedMaterialID]?.Tochildrequestheaderdata}
                />}
            </Box>
          )
      }
      <div>

      </div>
      <ReusableDialog dialogState={openDialog} openReusableDialog={handleDialogClickOpen} closeReusableDialog={handleDialogClose} dialogTitle="Warning" dialogMessage={dialogMessage} showCancelButton={false} handleOk={handleOkDialog} handleDialogConfirm={handleDialogClose} dialogOkText={"OK"} dialogSeverity="danger" />
      {openViews && (
        <Dialog fullWidth maxWidth={false} open={true} onClose={viewsClose} sx={{ display: "flex", justifyContent: "center" }} disableEscapeKeyDown>
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle sx={{ backgroundColor: "#EAE9FF", marginBottom: ".5rem" }}>
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>Select Views</span>
            </DialogTitle>
            <DialogContent sx={{ paddingBottom: ".5rem" }}>
              <Box display="flex" alignItems="center" sx={{ flex: 1, padding: "22px 0px", gap: "5px" }}>
                <Autocomplete
                  size="small"
                  multiple
                  fullWidth
                  options={allViews}
                  disabled={disableCheck}
                  disableCloseOnSelect
                  value={selectedViews?.filter((option) => option !== "Sales-Plant")}
                  onChange={(event, newValue) => {
                    setSelectedViews([fixedOption, ...newValue.filter((option) => option !== fixedOption)]);
                    handleCellEdit({ id: rowId, field: "views", value: newValue });
                  }}
                  getOptionDisabled={(option) => option === fixedOption}
                  renderOption={(props, option, { selected }) => {
                    const selectedExtendedView = extendedViews.find(
                      (val) => val?.material === selectedMaterialRow?.materialNumber
                    );


                    const isExtended = selectedExtendedView?.views?.includes(option) || false;
                    return (
                      <li {...props}>
                        <Checkbox checked={selected || option == "Basic Data"} sx={{ marginRight: 1 }} />
                        {option} {isExtended ? "(extended)" : ""}
                      </li>
                    );
                  }}
                  renderTags={(tagValue, getTagProps) =>
                    tagValue.map((option, index) => {
                      const { key, ...tagProps } = getTagProps({ index });
                      const selectedExtendedView = extendedViews.find(
                        (val) => val?.material === selectedMaterialRow?.materialNumber
                      );


                      const isExtended = selectedExtendedView?.views?.includes(option) || false;
                      return (
                        <Chip
                          key={key}
                          label={`${option} ${isExtended ? "(extended)" : ""}`}
                          {...tagProps}
                          disabled={option === fixedOption}
                        // disabled={option == "Basic Data(extended)"}
                        />
                      );
                    })
                  }
                  renderInput={(params) => <TextField {...params} label="Select Views" />}
                />

                <Button variant="contained" disabled={disableCheck} size="small" onClick={() => handleSelectAll()}>
                  Select all
                </Button>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setOpenViews(false);
                }}
                color="error"
                variant="outlined"
                sx={{
                  height: 36,
                  minWidth: "3.5rem",
                  textTransform: "none",
                  borderColor: "#cc3300",
                  fontWeight: 500,
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setOpenViews(false), handleCellEdit({ id: rowId, field: "views", value: selectedViews });
                }}
                variant="contained"
              >
                OK
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      )}

      {openOrgData && (
        <Dialog
          fullWidth
          maxWidth="xl"
          open
          onClose={viewsClose}
          disableEscapeKeyDown
          sx={{
            '& .MuiDialog-paper': {
              padding: 2,
              borderRadius: 2,
            },
          }}
        >
          <DialogTitle
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              backgroundColor: '#EAE9FF',
            }}
          >
            <DescriptionIcon fontSize="small" />
            {isCopyOrg ? <span>Select org data for copy</span> : <span>Select org data to be extended</span>}
            <IconButton onClick={viewsClose} sx={{ position: 'absolute', right: 15 }} >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ padding: 0 }}>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    {!isCopyOrg && <TableCell align="center">S NO.</TableCell>}
                    <TableCell align="center">Sales Org</TableCell>
                    <TableCell align="center">Distribution Channel</TableCell>
                    <TableCell align="center">Plant</TableCell>
                    <TableCell align="center">Storage Location</TableCell>
                    {initialPayload?.Region !== REGION_CODE.EUR && (
                    <TableCell align="center">Warehouse</TableCell>
                    )}
                    <TableCell align="center">MRP Profile</TableCell>
                    {orgRow?.length > 1 && !isCopyOrg && <TableCell align="center">Action</TableCell>}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fieldData?.map((row, index) => {
                    return (
                      <TableRow key={index} sx={{ padding: "12px" }}>
                        {!isCopyOrg &&<TableCell>
                          <Typography variant="body2">
                            {index + 1}
                          </Typography>
                        </TableCell>
                        }
                        <TableCell>
                          <SingleSelectDropdown
                            options={dropDownData["Sales Organization"]}
                            value={!isCopyOrg ? row?.salesOrg : copyOrgRowFieldData?.salesOrg}
                            onChange={(newValue) => handleSalesOrgSel("Sales Organization", newValue, index)}
                            placeholder="Select Sales Org"
                            disabled={disableCheck}
                            isFieldError={false}
                            minWidth={165}
                            isLoading={isDropdownLoading["Sales Organization"]}
                          />
                        </TableCell>
                        <TableCell>
                          <SingleSelectDropdown
                            options={!isCopyOrg ? row.dc?.options : copyOrgRowFieldData?.dc?.options}
                            value={!isCopyOrg ? row.dc?.value : copyOrgRowFieldData?.dc?.value}
                            onChange={(newValue) => !isCopyOrg 
                              ? setDc(newValue, index) 
                              : setCopyOrgRowFieldData((prev) => ({ 
                                  ...prev, 
                                  dc: { value: newValue, options: copyOrgRowFieldData?.dc?.options } 
                                }))}
                            placeholder="Select DC"
                            disabled={disableCheck}
                            isFieldError={false}
                            minWidth={165}
                            isLoading={isDropdownLoading["Distribution Channel"][index]}
                          />
                        </TableCell>
                        <TableCell>
                          <SingleSelectDropdown
                            options={dropDownData["Plant"] || []}
                            value={!isCopyOrg ? row.plant?.value : copyOrgRowFieldData?.plant?.value}
                            onChange={(newValue) => handlePlant(newValue, index)}
                            placeholder="Select Plant"
                            disabled={disableCheck}
                            isFieldError={false}
                            minWidth={165}
                            isLoading={isDropdownLoading["Plant"][index]}
                          />
                        </TableCell>

                        <TableCell>
                          <SingleSelectDropdown
                            options={!isCopyOrg ? row?.sloc?.options : copyOrgRowFieldData?.sloc?.options}
                            value={!isCopyOrg ? row?.sloc?.value : copyOrgRowFieldData?.sloc?.value}
                            onChange={(newValue) => !isCopyOrg ? setSloc(newValue, index) : setCopyOrgRowFieldData((prev) => ({ ...prev, sloc: { value: newValue, options: copyOrgRowFieldData?.sloc?.options } }))}
                            placeholder="Select Sloc"
                            disabled={disableCheck}
                            isFieldError={false}
                            minWidth={165}
                            isLoading={isDropdownLoading["Storage Location"][index]}
                          />
                        </TableCell>
                        {initialPayload?.Region !== REGION_CODE.EUR && (
                        <TableCell>
                          <SingleSelectDropdown
                            options={dropDownData["warehouse"] || []}
                            value={!isCopyOrg ? row?.warehouse?.value : copyOrgRowFieldData?.warehouse?.value}
                            onChange={(newValue) => !isCopyOrg ? setWarehouse(newValue, index) : setCopyOrgRowFieldData((prev) => ({ ...prev, warehouse: { value: newValue, options: copyOrgRowFieldData?.warehouse?.options } }))}
                            placeholder="Select Warehouse"
                            disabled={disableCheck}
                            isFieldError={false}
                            minWidth={165}
                            isLoading={isDropdownLoading["warehouse"][index]}
                          />
                        </TableCell>
                        )}
                        <TableCell>
                          <SingleSelectDropdown
                            options={dropDownData["Mrp Profile"] || []}
                            value={!isCopyOrg ? row.mrpProfile : copyOrgRowFieldData?.mrpProfile}
                            onChange={(newValue) => !isCopyOrg ? setMrpDefault(newValue, index) : setCopyOrgRowFieldData((prev) => ({ ...prev, mrpProfile: newValue }))}
                            placeholder="Select MRP Profile"
                            disabled={disableCheck}
                            isFieldError={false}
                            minWidth={165}
                            isLoading={isDropdownLoading["Mrp Profile"]}
                          />
                        </TableCell>
                        {orgRow.length > 1 && (
                          <TableCell align="right">
                            <IconButton 
                              size="small" 
                              color="primary" 
                              disabled={disableCheck}
                              onClick={() => {
                                setOpenOrgCopyModal(true);
                                setLengthOfOrgRow({
                                  orgRowLength: orgRow.length,
                                  copyFor: index
                                });
                              }}
                              style={{ display: index === 0 ? 'none' : 'inline-flex' }}
                            >
                              <FileCopyIcon /> 
                            </IconButton>
                            <IconButton style={{ display: index === 0 ? 'none' : 'inline-flex' }} size="small" color="error" onClick={() => removeOrgRow(index)}>
                              <DeleteOutlineOutlinedIcon />
                            </IconButton>
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </DialogContent>

          <DialogActions sx={{ justifyContent: 'flex-end', gap: 0.5 }}>
            {!isCopyOrg && <Button onClick={AddAnotherOrg} disabled={disableCheck} variant="contained">
              + Add
            </Button>}
            <Button
              onClick={() => {
                setOpenOrgData(false);
                if (orgRow[0].plant){
                  handleCellEdit({ id: rowId, field: 'orgData', value: orgRow });
                  if(!isCopyOrg) {
                    fetchOrgSpecificData(orgRow);
                    const updatedRows = rows?.map(row => {
                      if (row?.id === rowId) {
                        return { ...row, orgData: orgRow };
                      }
                      return row;
                    });
                    dispatch(setMaterialRows(updatedRows));
            
                  }
                } 
                const plantIds = orgRow
                        .filter(row => row.plant?.value?.code)
                        .map(row => row.plant?.value?.code);
                      if (plantIds.length > 0) {
                        addOtherViews(plantIds);
                      }
                if (isCopyOrg) {
                  setCopyOrgRowArray(prevArray => {
                    const index = prevArray.findIndex(item => item.id === copyOrgRowFieldData.id);
                    if (index !== -1) {
                      return prevArray.map((item, i) =>
                        i === index ? { ...item, ...copyOrgRowFieldData } : item
                      );
                    } else {
                      return [...prevArray, copyOrgRowFieldData];
                    }
                  });
                  getView(copyOrgRowFieldData, orgRow, initialPayload, selectedViews)
                }
              }}
              variant="contained"
            >
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      )}
      {openOrgCopyModal && (
        <OrgDataCopyModal 
          open={openOrgCopyModal}
          onClose={() => setOpenOrgCopyModal(false)}
          title={HEADINGS.COPY_ORG_DATA_VALES_HEADING}
          selectedMaterialPayload={selectedMaterialPayload}
          lengthOfOrgRow={lengthOfOrgRow}
          materialID={selectedMaterialID}
          orgRows={orgRow}
        />
      )}


      {messageDialogMessage && <ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={() => setOpenSnackbar(false)} />}
      {<ExtendMaterialSearch openSearchMat={openSearchMat} materialOptions={materialOptions} handleMatInputChange={handleMatInputChange} inputState={inputState} setOpenSearchMat={setOpenSearchMat} dropDownData={dropDownData} AddCopiedMaterial={AddCopiedMaterial} />}
      <ToastContainer />
    </div>
  );
};

export default RequestDetailsForExtend;
