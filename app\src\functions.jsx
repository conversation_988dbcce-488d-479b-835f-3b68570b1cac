import domtoimage from "dom-to-image";
import axios from "axios";
import moment from "moment";
import ManageAccountsOutlinedIcon from "@mui/icons-material/ManageAccountsOutlined";
import { IconButton, Tooltip, Button, Stack, Typography} from "@mui/material";
import { iconButton_SpacingSmall, button_Primary } from "./components/common/commonStyles";
import { destination_ITM, destination_Po } from "./destinationVariables";
import Store from "./app/store";
import Excel from "exceljs";
import { saveAs } from "file-saver";
import { doAjax } from "./components/Common/fetchService";
import jsPDF from "jspdf";
import generatePDF, { Margin, Resolution, usePDF } from "react-to-pdf";
import ReactDOMServer from "react-dom/server";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { VALIDATION_STATUS ,MATERIAL_VIEWS,REGION_CODE} from "@constant/enum";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import WarningIcon from "@mui/icons-material/Warning";
import { CircularProgress } from '@mui/material';
import InfoIcon from "@mui/icons-material/Info";
import { colors } from "@constant/colors";
import { convertSAPDateForCalendar } from './helper/helper';
import { EAN_CATEGORIES } from "./constant/enum";

let timeStampToDate = (timeStamp) => {
  let dateFormat = new Date(parseInt(timeStamp));
  const appSettings = Store.appSettings["Format"];
  let datestamp = dateFormat.getDate() + "-" + (dateFormat.getMonth() + 1) + "-" + dateFormat.getFullYear();

  return moment(datestamp, "DD-MM-YYYY").format(appSettings.date);
};
let getTimestamp = (aDate) => {
  if (aDate) {
    const presentDate = new Date();
    const backDate = new Date();
    backDate.setDate(backDate.getDate() - 1);
    // console.log(`sent date ${new Date(aDate)} - created date ${new Date(aDate.createdAt)}`)
    if (new Date(aDate).toDateString() === presentDate.toDateString()) {
      return `Today ${moment(aDate).format("hh:mm A")}`;
    }
    if (new Date(aDate).toDateString() === backDate.toDateString()) {
      return `Yesterday ${moment(aDate).format("hh:mm A")}`;
    }
    return `${moment(aDate).format("DD MMM YYYY hh:mm A")}`;
  }
};

export const showToast = (message, type = "default", options = {}) => {
  if (!toast.isActive(message)) {
    const {
      position = "bottom-left",
      autoClose = 3000,
      hideProgressBar = true,
      toastLoader = false, 
      isLoading = false,
      largeWidth = false,
    } = options;

    const iconMap = {
      success: <CheckCircleIcon sx={{ color:colors?.success?.bright, fontSize: 20 }} />,
      error: <ErrorIcon sx={{ color:colors?.error?.red, fontSize: 20 }} />,
      warning: <WarningIcon sx={{ color: colors?.warning?.orange, fontSize: 20 }} />,
      info: <InfoIcon sx={{ color:colors?.info?.pale, fontSize: 20 }} />,
      default: null,
    };
    const toastContent = (
      <Stack direction="row" spacing={1.5} alignItems="center" 
      sx={{
        maxWidth: largeWidth ? 600 : 320, // Dynamically Set Width
        wordBreak: "break-word",
      }}>
        {isLoading ? <CircularProgress size={20} color="inherit" /> : iconMap[type]}
        <Typography sx={{ fontSize: "0.875rem" }}>{message}</Typography>
      </Stack>
    );

    if (toastLoader) {
      toast.loading(message, {
        position,
        theme: "dark",
        toastId: message,
      });
    } else {
      toast(toastContent, {
        type,
        position,
        autoClose,
        hideProgressBar,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        theme: "dark",
        toastId: message,
        icon:false
      });
    }
  }
};

let idGenerator = (ref) => {
  let date = new Date();
  // let randomNumb = Math.floor((Math.random() * 9999) - 1)
  const month = date.getMonth() < 10 ? "0" + (date.getMonth() + 1).toString() : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();

  // return `${ref}${(date.getUTCFullYear()).toString().slice(-2)}${date.getMonth()+1}${date.getDate()}${date.getHours()}${date.getMinutes()}${(date.getSeconds())}`
  return `${ref}${date.getUTCFullYear().toString().slice(-2)}${month}${day}${hour}${minutes}${seconds}`;
};
let ASNidGenerator = (ref) => {
  let date = new Date();
  // let randomNumb = Math.floor((Math.random() * 9999) - 1)
  const month = date.getMonth() < 10 ? "0" + (date.getMonth() + 1).toString() : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds = date.getMilliseconds() < 10 ? "00" + date.getMilliseconds() : date.getMilliseconds();
  // return `${ref}${(date.getUTCFullYear()).toString().slice(-2)}${date.getMonth()+1}${date.getDate()}${date.getHours()}${date.getMinutes()}${(date.getSeconds())}`
  return `${ref}${date.getUTCFullYear().toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};
// let serviceRequestIcon =   <MiscellaneousServicesOutlinedIcon />
let RETidGenerator = (ref) => {
  let date = new Date();
  // let randomNumb = Math.floor((Math.random() * 9999) - 1)
  const month = date.getMonth() < 10 ? "0" + (date.getMonth() + 1).toString() : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds = date.getMilliseconds() < 10 ? "00" + date.getMilliseconds() : date.getMilliseconds();
  // return `${ref}${(date.getUTCFullYear()).toString().slice(-2)}${date.getMonth()+1}${date.getDate()}${date.getHours()}${date.getMinutes()}${(date.getSeconds())}`
  return `${ref}${date.getUTCFullYear().toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};

let SESIDGenerator = (ref) => {
  let date = new Date();
  const month = date.getMonth() < 10 ? "0" + (date.getMonth() + 1).toString() : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds = date.getMilliseconds() < 10 ? "00" + date.getMilliseconds() : date.getMilliseconds();
  return `${ref}${date.getUTCFullYear().toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};

let PlanningMgmtIDGenerator = (ref) => {
  let date = new Date();
  const month = date.getMonth() < 10 ? "0" + (date.getMonth() + 1).toString() : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds = date.getMilliseconds() < 10 ? "00" + date.getMilliseconds() : date.getMilliseconds() < 100 ? "0" + date.getMilliseconds() : date.getMilliseconds();
  return `${ref}${date.getUTCFullYear().toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};

let getColor_Status = (status, colorObj) => {
  let modified_Status = status.toLowerCase().split(" ").join("");
  // console.log(modified_Status, colorObj )
  if (modified_Status in colorObj) {
    return colorObj[modified_Status];
  } else {
    return "none";
  }
};
let getColor_priority = (priority, colorObj) => {
  let modified_Priority = priority
    .toLowerCase()
    .replace(/[" "]/gi, "")
    .replace(/[0-9]/gi, "")
    .replace(/[-|.|@|#|$|%|^|&|*|(|)|<|>|?|"|:|;|}|{|]/gi, "");

  if (modified_Priority in colorObj) {
    return colorObj[modified_Priority];
  } else {
    return "none";
  }
};

let controller_UrlRedirecting = (value = null, type = null) => {
  let lowercasedValue = value?.toLowerCase();
  let lowercasedType = type?.toLowerCase();
  if (lowercasedValue.includes("com")) {
    return ``;
  }
  if (value && type) {
    if (lowercasedType === "e-invoice") {
      return `/invoices/singleInvoice/${value}`;
    }
    if (lowercasedType === "service request") {
      return `/serviceRequest/details/${value}`;
    }
    if (lowercasedType === "purchase order") {
      return `/purchaseOrder/management/singlePurchaseOrder/${value}`;
    }
    if (lowercasedType === "daily production report") {
      return `/purchaseOrder/DPR/singleDPR/${value}`;
    }
    if (lowercasedType === "advanced shipment notification") {
      return `/purchaseOrder/ASN/details/${value}`;
    }
    if (lowercasedType === "po workbench") {
      return `/purchaseOrder/confirmationTracker/taskDetail/${value}`;
    }
    if (lowercasedType === "invoice tracker") {
      return `/invoices/singleInvoice/${value}`;
    }
    if (lowercasedType === "invoice workbench") {
      return `/invoices/workbench/singleInvoice/${value}`;
    }
    if (lowercasedType === "return") {
      return `/ReturnManagement/SingleReturnOrder/${value}`;
    }
  }
  if (type && value === null) {
    if (lowercasedType === "home") {
      return `/`;
    }
    if (lowercasedType === "dashboard") {
      return `/dashboard`;
    }
    if (lowercasedType === "purchase order") {
      return `/purchaseOrder/management`;
    }
    if (lowercasedType === "daily production report") {
      return `/DPR`;
    }
    if (lowercasedType === "advance shipment notification") {
      return `/purchaseOrder/advanceShipmentManagement`;
    }
    if (lowercasedType === "po workbench") {
      return ` /workbench`;
    }
    if (lowercasedType === "e-invoice") {
      return `/invoices`;
    }
    if (lowercasedType === "invoice tracker") {
      return `/invoices`;
    }
    if (lowercasedType === "invoice workbench") {
      return `/invoices/workbench`;
    }
    if (lowercasedType === "service request") {
      return `/serviceRequest`;
    }
  }
};

const handleMultipleDownload = (array, key, fileName) => {
  array.forEach((item, index) => {
    axios({
      url: item[key],
      method: "GET",
      responseType: "blob", // important
    }).then((response) => {
      const href = URL.createObjectURL(response.data);

      // create "a" HTML element with href to file & click
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", item[fileName]); //or any other extension
      document.body.appendChild(link);
      link.click();

      // clean up "a" element & remove ObjectURL
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    });
  });
};

async function setStatusRecord(poNumber, status, date, module) {
  var formData = new FormData();

  formData.set("poNumber", poNumber);
  formData.set("status", status);
  formData.set("date", date);
  formData.set("module", module);
  await fetch(`/${destination_Po}/poFlow/save`, {
    method: "POST",
    body: formData,
  })
    .then((res) => res.json())
    .then((data) => console.log(data));
}

class createServiceRequestForm {
  constructor(state, setstate, callback) {
    this.state = state;
    this.setState = setstate;
    this.icon = <ManageAccountsOutlinedIcon />;
    this.reload = callback ? callback : null;
  }

  button = () => {
    let accessList = Store.getState().masterData.accessList;
    if (accessList?.["ServiceRequest"]) {
      return (
        <Tooltip title="Create Service Request">
          <IconButton sx={iconButton_SpacingSmall} onClick={this.open}>
            {this.icon}
          </IconButton>
        </Tooltip>
      );
    } else {
      return <></>;
    }
  };
  createSR_Button = () => {
    return (
      <Button size="small" variant="contained" sx={{ ...button_Primary }} onClick={this.open}>
        Create Service Request
      </Button>
    );
  };
  component = (Data, ids = []) => {
    return <ServiceRequestForm open={this.state.open} controller={this.controller} data={Data} transactionIds={ids} />;
  };
  open = () => {
    // navigate('/serviceRequest/createServiceRequest')
    this.setState({ open: true });
  };
  controller = (ref) => {
    switch (ref) {
      case "CLOSE_FORM":
        this.setState({ open: false });
        break;
      case "SUCCESS":
        this.setState({ open: false });
        this.reload && typeof this.reload === "function" && this.reload();
        break;
      case "FAILED":
        break;
      default:
        return null;
    }
  };
}
let formValidator = (stateData, keyList_Received, setErrorItemList) => {
  let keyList = [...keyList_Received];
  let ErrorItemList = [];
  keyList.map((item) => !(stateData[item] !== "" && stateData[item] !== null && stateData[item] !== undefined) && ErrorItemList.push(item));

  ErrorItemList.length > 0 ? setErrorItemList(ErrorItemList) : setErrorItemList([]);

  return keyList.every((item) => stateData[item] !== "" && stateData[item] !== null && stateData[item] !== undefined);
};
// let formValidatorForArray = (stateData, keyList_Received, setErrorItemList) => {
//   let keyList = [...keyList_Received];
//   let ErrorItemList = [];
//   keyList.map(
//     (item) =>
//       !(
//         stateData[item] !== "" &&
//         stateData[item] !== null &&
//         stateData[item] !== undefined
//       ) && ErrorItemList= stateData
//   );

//   ErrorItemList.length > 0
//     ? setErrorItemList((prev)=>[...prev,ErrorItemList])
//     : setErrorItemList(null);

//   return keyList.every(
//     (item) =>
//       stateData[item] !== "" &&
//       stateData[item] !== null &&
//       stateData[item] !== undefined
//   );
// };
const capitalize = (str) => {
  if (str === null) {
    return "";
  }
  const arr = str.split(" ");
  for (var i = 0; i < arr.length; i++) {
    arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
  }

  const str2 = arr.join(" ");
  return str2;
};

const capitalizeByWord = (str = "", exWords = []) => {
  const arr = str.split(" ");
  const capitalizedArr = arr.map((word) => (exWords.includes(word) ? word : `${word[0].toUpperCase()}${word.slice(1).toLowerCase()}`));
  return capitalizedArr.join(" ");
};

// <-- Function for taking screenshot (Export button) -->

let captureScreenShot = async (imageName) => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("body")[0];
  const container = document.getElementById("print_screen");
  const app_container = document.getElementById("app_Container");

  const printScreen = document.querySelector(".printScreen");

  html.style.cursor = "progress";

  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = container; //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
  const newWidth = data?.scrollWidth - data?.clientWidth;

  if (newWidth > data?.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  // html.style.width = htmlWidth + "px";
  // body.style.width = bodyWidth + "px";
  // app_container.style.height = "100vh";
  // container.style.height = inherit'

  app_container.style.overflow = "hidden";

  domtoimage.toPng(data, { quality: 1, bgcolor: "#fff" }).then(function (dataUrl) {
    var link = document.createElement("a");
    link.download = `${imageName}-${moment(new Date()).format("DD-MMM-YY")}.png`;
    link.href = dataUrl;
    link.click();
    // body.style.height = null;
    // html.style.overflow = null;
    html.style.width = null;
    body.style.width = null;
    container.style.height = null;
    container.style.overflow = null;
    app_container.style.overflow = null;
    app_container.style.height = null;
    html.style.cursor = null;
  });
};

export const exportAsPDF = (element) => {
  // Default export is a4 paper, portrait, using millimeters for units
  const doc = new jsPDF();
  let elementHtml = ReactDOMServer.renderToString(element);
  doc.html(elementHtml, {
    callback: function (doc) {
      // Save the PDF
      doc.save(`summary-report.pdf`);
    },
    resolution: Resolution.HIGH,
    page: {
      // margin is in MM, default is Margin.NONE = 0
      margin: Margin.SMALL,
      // default is 'A4'
      format: "letter",
      // default is 'portrait'
      orientation: "landscape",
    },
    x: 15,
    y: 15,
    width: 170, //target width in the PDF document
    windowWidth: 1200, //window width in CSS pixels
  });
  // doc.save('test.pdf')
};

//GET USER EMAIL ID
const getUserEmailId = (input) => {
  try {
    if (input !== "" && input.includes("@")) {
      return input.split("- ")[1];
    } else {
      return "";
    }
  } catch (e) {
    console.log(e);
  }
};

//IWA Access check functions - return boolean
const checkIwaAccess = (data, module = null, feature = null) => {
  if (feature && data) {
    //if entity exists
    return data.includes(feature);
  }
};

const multiConfirmationCheck = (status, multiConfStatus) => {
  if (multiConfStatus) {
    if (["REQUIRES CONFIRMATION", "CONFIRMED", "PARTIALLY CONFIRMED", "FULLY CONFIRMED", "ASN TO BE INITIATED", "PRODUCTION TO BE INITIATED"].includes(status)) {
      return true;
    }
  } else {
    if (status === "REQUIRES CONFIRMATION") {
      return true;
    }
  }
  return false;
};

const generateAccessList = (jsonData) => {
  var accessList = {};
  var urlList = {};

  const processJson = (processdata) => {
    const pushItem = (item) => {
      accessList[item?.name] = item;
      urlList[item?.routhPath] = item;
    };
    processdata?.map((item) => {
      if (!item?.isAccessible) return;
      if (item?.isAccessible && !item?.childItems?.length) pushItem(item);
      if (item?.isAccessible && item?.childItems?.length) {
        pushItem(item);
        processJson(item?.childItems);
      }
    });
  };
  processJson(jsonData);

  return { accessList, urlList };
};
function validatePath(path, masterData) {
  if (masterData?.urlList[path]) {
    return true;
  } else {
    return false;
  }
}
let ValidNavigateTo = (path) => {
  let masterData = Store.getState().masterData;
  let navigate = masterData.navigate;

  // console.log(masterData?.urlList,'url')
  if (validatePath(path, masterData)) {
    navigate(path);
  }
  return null;
};

const updateTaskStatus = (action, id, comment, userEmail) => {
  var itmAction;
  if (action.toUpperCase() === "ACCEPT") {
    itmAction = "APPROVE";
  } else if (action.toUpperCase() === "REJECT") {
    itmAction = "REJECT";
  }
  doAjax(`/${destination_Po}/task/workflow-task-Id/${id}`, "get", (data) => {
    if (data?.data) {
      var payload = {
        action: itmAction,
        systemId: "SCP",
        userId: userEmail,
        taskId: data?.data,
        comment: comment,
      };
      doAjax(
        `/${destination_ITM}/v1/task/updateTaskStatus`,
        "post",
        (res) => {
          if (res.statusCode === 200) {
            return true;
          } else {
            return false;
          }
        },
        (error) => {
          console.log(error);
          return false;
        },
        payload
      );
    }
  });
};

const columns = [
  { header: "SYSTEM NAME", key: "systemName" },
  { header: "PROCESS NAME", key: "processName" },
  { header: "ID", key: "referenceId" },
  { header: "TASK DESC", key: "taskDesc" },
  { header: "BUSINESS STATUS", key: "businessStatus" },
  { header: "CREATED BY", key: "createdByName" },
  { header: "ASSIGNED TO", key: "ownerId" },
  // { header: "UPDATED BY", key: "updatedBy" },
  { header: "SLA STAUS", key: "taskSla" },
  { header: "ATTACHMENT COUNT", key: "attachmentCount" },
];

const savePDF = (data) => {
  const doc = new jsPDF();
  try {
    // Get table data
    const tableData = data.rows;

    // Set table columns (headers)
    const tableColumns = data.columns.map((column) => ({
      header: column.header,
      dataKey: column.key,
    }));

    // Add table to the PDF document
    doc.autoTable({
      head: [tableColumns.map((col) => col.header)],
      body: tableData.map((row) => tableColumns.map((col) => row[col.dataKey])),
    });

    // Save the PDF file
    doc.save(`${data.fileName}.pdf`);
  } catch (error) {
    console.log(error, "pdf error");
    // Handle error if needed
  }
};

const saveExcel = async (data) => {
  const workbook = new Excel.Workbook();
  try {
    // creating one worksheet in workbook
    const worksheet = workbook.addWorksheet(data.fileName);
    worksheet.columns = data.columns;

    // updated the font for first row.
    worksheet.getRow(1).font = { bold: true };
    let dataTypeList = [];

    // loop through all of the columns and set the alignment with width.
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true };
      cell.alignment = { horizontal: "center", vertical: "middle" };

      cell.border = {
        top: { style: "thin", color: { argb: colors?.basic?.black } },
        bottom: { style: "thin", color: {  argb: colors?.basic?.black } },
        left: { style: "thin", color: {  argb: colors?.basic?.black } },
        right: { style: "thin", color: {  argb: colors?.basic?.black } },
      };
    });
    data.rows?.forEach((singleData, index) => {
      let tData = { ...singleData };
      dataTypeList.forEach((item) => {
        if (item.type === "date") {
          tData[item.key] = moment(tData[item.key]).format("DD-MMM-YYYY");
        }
      });
      worksheet.addRow(tData);
    });

    // loop through all of the rows and set the outline style.
    worksheet.eachRow({ includeEmpty: false }, (row) => {
      // store each cell to currentCell
      const currentCell = row._cells;
      // loop through currentCell to apply border only for the non-empty cell of excel
      currentCell.forEach((singleCell) => {
        // store the cell address i.e. A1, A2, A3, B1, B2, B3, ...
        const cellAddress = singleCell._address;
        // apply border
        worksheet.getCell(cellAddress).border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });

    // write the content using writeBuffer
    const buf = await workbook.xlsx.writeBuffer();

    // download the processed file
    saveAs(new Blob([buf]), `${data.fileName}.xlsx`);
    // if (searchResults?.length > 0) {
    //   let toastMessage = "task downloaded successfully";
    //   if (searchResults?.length > 1) {
    //     toastMessage = "tasks downloaded successfully";
    //   }
    //   showToastMessage(`${searchResults.length} ${toastMessage}`);
    // }
  } catch (error) {
    console.log(error, "excel error");
    // showToastMessage("Something went wrong");
  } finally {
    // removing worksheet's instance to create new one
    // workbook.removeWorksheet(workSheetName);
  }
};
const saveExcelMultiSheets = async (data) => {
  const workbook = new Excel.Workbook();
  try {
    data.forEach((iData) => {
      // creating one worksheet in workbook
      const worksheet = workbook.addWorksheet(iData.sheetName);
      worksheet.columns = iData.columns;

      // updated the font for first row.
      worksheet.getRow(1).font = { bold: true };
      let dataTypeList = [];

      // loop through all of the columns and set the alignment with width.
      worksheet.columns.forEach((column) => {
        if (column._header?.toLowerCase().split(" ").join("").includes("date")) {
          dataTypeList.push({
            key: column._key,
            type: "date",
          });
        }

        column.width = column.header.length + 5;
        column.alignment = { horizontal: "center" };
      });

      // loop through data and add each one to worksheet
      iData.rows?.forEach((singleData, index) => {
        // console.log(singleData)
        // worksheet.getRow(1).getCell(index).value =
        let tData = { ...singleData };
        dataTypeList.forEach((item) => {
          if (item.type === "date") {
            tData[item.key] = moment(tData[item.key]).format("DD-MMM-YYYY");
          }
        });
        worksheet.addRow(tData);
      });

      // loop through all of the rows and set the outline style.
      worksheet.eachRow({ includeEmpty: false }, (row) => {
        // store each cell to currentCell
        const currentCell = row._cells;

        // loop through currentCell to apply border only for the non-empty cell of excel
        currentCell.forEach((singleCell) => {
          // store the cell address i.e. A1, A2, A3, B1, B2, B3, ...
          const cellAddress = singleCell._address;

          // apply border
          worksheet.getCell(cellAddress).border = {
            top: { style: "thin" },
            left: { style: "thin" },
            bottom: { style: "thin" },
            right: { style: "thin" },
          };
        });
      });
    });

    const buf = await workbook.xlsx.writeBuffer();

    saveAs(new Blob([buf]), `${data[0].fileName}.xlsx`);
  } catch (error) {
    console.log(error, "excel error");
  } finally {
    // removing worksheet's instance to create new one
    // workbook.removeWorksheet(workSheetName);
  }
};

//Method to get role as per region
export const getRoleForWorkflow = (region, rolesArray) => {
  // Define priority of roles for selection
  const priorityRoles = ["CA-MDG-MRKTNG-US"];

  // Filter roles based on the region
  const filteredRoles = rolesArray.filter((role) => role.includes(region));

  // Find the highest-priority role
  for (const priorityRole of priorityRoles) {
    if (filteredRoles.includes(priorityRole)) {
      return priorityRole;
    }
  }

  // Return null if no matching role is found
  return null;
};

//method to convert display response into redux structure
export const transformApiResponseToReduxPayload = (apiResponse, storedRows) => {
  let counter = storedRows?.length*10 || 0;
  const nextLineNumber = () => {
    counter += 10;
    return counter;
  };
  const payload = {
    payloadData: {
      ReqCreatedBy: apiResponse[0]?.Torequestheaderdata?.ReqCreatedBy,
      RequestStatus: apiResponse[0]?.Torequestheaderdata?.RequestStatus,
      ReqCreatedOn: new Date().toISOString(),
      ReqUpdatedOn: new Date().toISOString(),
      RequestType: apiResponse[0]?.Torequestheaderdata?.RequestType,
      RequestDesc: apiResponse[0]?.Torequestheaderdata?.RequestDesc,
      RequestPriority: apiResponse[0]?.Torequestheaderdata?.RequestPriority,
      LeadingCat: apiResponse[0]?.Torequestheaderdata?.LeadingCat,
      RequestId: apiResponse[0]?.Torequestheaderdata?.RequestId,
      TotalIntermediateTasks: apiResponse[0]?.TotalIntermediateTasks,
      IntermediateTaskCount: apiResponse[0]?.IntermediateTaskCount,
      Division: apiResponse[0]?.Torequestheaderdata?.Division,
      Region: apiResponse[0]?.Torequestheaderdata?.Region,
    },
    errorFields: [],
    requiredFields: [],
    additionalData: [],
    unitsOfMeasureData: [],
    taxData: [],
    requiredFieldsGI: ["BusinessJustification"],
    singleMatPayload: {
      BusinessJustification: "",
      ChoosePriorityLevel: "Medium",
    },
    generalInformation: [],
    mandatoryFields: [],
    errorData: {},
    fcRows:[],
    changeFieldRows:[],
    changeFieldRowsDisplay:{},
    requestorPayload: {},
    changeLogData:{},
    filteredButtons:[],
    dynamicKeyValues: {},
    dataLoading: false,
    isSubmitDisabled: true,
    newRowIds: [],
    selectedRows: [],
    unselectedRows: [],
    templateArray: [],
    whseList: [],
    matNoList: [],
  };

  function getAccountingId(accountingData) {
    return accountingData?.map((item) => item?.AccountingId);
  }

  // Process each item in the API response
  apiResponse.forEach((item) => {
    const dynamicKey = item?.MaterialId?.toString();
    payload[dynamicKey] = {
      headerData: {
        id: dynamicKey,
        clientId: item?.Toclientdata?.ClientId,
        AccountingId: getAccountingId(item?.Toaccountingdata),
        included: true,
        industrySector: {
          code: item?.IndSector || "",
        },
        materialType: {
          code: item?.MatlType || "",
        },
        lineNumber: item?.lineNumber ?? nextLineNumber() ?? null,
        materialNumber: item?.Material || "",
        globalMaterialDescription: item?.Description || "",
        Bom	: item?.Bom	 || "",
        sourceList : item?.SourceList || "",
        PIR : item?.PIR || "",
        Uom : item?.Uom?.code || item?.Uom || "",
        Category : item?.Category?.code || item?.Category || "",
        Relation : item?.Relation?.code || item?.Relation || "",
        Usage : item?.Usage || "",
        views: [
          ...(item?.ViewNames || "").split(",").map((view) => view.trim()).filter(Boolean),
          ...(item?.Bom ? ["BOM"] : []),
          ...(item?.SourceList ? ["Source List"] : []),
          ...(item?.PIR ? ["PIR"] : []),
        ],
        validated: VALIDATION_STATUS.default,
        orgData: item?.OrgData || [],
      },
      payloadData: {
        "Basic Data": {
          basic: {
            BaseUom: item?.Toclientdata?.BaseUom || "",
            MatlGroup: item?.Toclientdata?.MatlGroup || "",
            Division: item?.Torequestheaderdata?.Division || item?.Toclientdata?.Division || "",
            ProdHier: item?.Toclientdata?.ProdHier || "",
            VolumeUnit: item?.Toclientdata?.VolumeUnit || "",
            UnitOfWt: item?.Toclientdata?.UnitOfWt || "",
            GItemCat: item?.Toclientdata?.GItemCat || "",
            BasicMatl: item?.Toclientdata?.BasicMatl || "",
            Hazmatprof: item?.Toclientdata?.Hazmatprof || "",
            OldMatNo: item?.Toclientdata?.OldMatNo || "",
            Extmatlgrp: item?.Toclientdata?.Extmatlgrp || "",
            DsnOffice: item?.Toclientdata?.DsnOffice || "",
            NetWeight: item?.Toclientdata?.NetWeight || "",
            HighVisc: item?.Toclientdata?.HighVisc || "",
            IndSector: item?.Toclientdata?.IndSector || "",
            HazMatNo: item?.Toclientdata?.HazMatNo || "",
            StdDescr: item?.Toclientdata?.StdDescr || "",
            ShelfLife: item?.Toclientdata?.ShelfLife || "",
            CSalStatus : item?.Toclientdata?.CSalStatus || "",
            IntlPoPrice : item?.Toclientdata?.IntlPoPrice || "",
            PryVendor : item?.Toclientdata?.PryVendor || "",
            PlanningArea : item?.Toclientdata?.PlanningArea || "",
            PlanningFactor : item?.Toclientdata?.PlanningFactor || "",
            ReturnMatNumber : item?.Toclientdata?.ReturnMatNumber || "",
            ParentMatNumber : item?.Toclientdata?.ParentMatNumber || "",
            DiversionControlFlag : item?.Toclientdata?.DiversionControlFlag || "",
            MatGroupPackagingMat : item?.Toclientdata?.MatGroupPackagingMat || "",
            ProdAlloc: item?.Toclientdata?.ProdAlloc || "",
            Pvalidfrom: convertSAPDateForCalendar(item?.Toclientdata?.Pvalidfrom) || "",
          },
        },
        Purchasing: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            PurGroup: plant.PurGroup || "",
            PurStatus: plant.PurStatus || "",
            PlantId: plant?.PlantId,
            CommCode: plant.CommCode || "",
            CommCoUn:plant.CommCoUn || "",
            Countryori: plant.Countryori || "",
            IndPostToInspStock: plant.IndPostToInspStock || "",
            HtsCode: plant.HtsCode || "",
          };
          return acc;
        }, {}),
        Accounting : (item.Toaccountingdata || []).reduce((acc, accounting,index) => {
          acc[accounting?.ValArea] = {
            AccountingId: accounting.AccountingId || "",
            ValClass : accounting.ValClass || '',
            PriceCtrl : accounting.PriceCtrl || '',
            PriceUnit : accounting.PriceUnit || '',
            StdPrice : accounting.StdPrice || ''
          };
          return acc;
        }, {}),
        Costing : (item.Toaccountingdata || []).reduce((acc, accounting,index) => {
          acc[accounting?.ValArea] = {
            AccountingId: accounting.AccountingId || "",
            OrigMat : accounting.OrigMat || "",
            LotSize : item.Toplantdata?.[index]?.LotSize || '',
            ProfitCtr: item.Toplantdata?.[index]?.ProfitCtr || "",
            VarianceKey : item.Toplantdata?.[index]?.VarianceKey || '',
            MovingPr : accounting.MovingPr || '',
            StdPrice : accounting.StdPrice || ''
          };
          return acc;
        }, {}),
        MRP: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[`${plant?.Plant}`] = {
            ...plant,
            MrpType: plant.MrpType || "",
            MrpCtrler: plant.MrpCtrler || "",
            ProcType: plant.ProcType || "",
            Backflush: plant.Backflush || "",
            PeriodInd: plant.PeriodInd || "",
            PlanStrgp: plant.PlanStrgp || "",
            Consummode: plant.Consummode || "",
            Availcheck: plant.Availcheck || "",
            DepReqId: plant.DepReqId || "",
            Lotsizekey: plant.Lotsizekey || "",
            SaftyTId: plant.SaftyTId || "",
            SmKey: plant.SmKey || "",
            MixedMrp: plant.MixedMrp || "",
            GrpReqmts: plant.GrpReqmts || "",
            Minlotsize: plant.Minlotsize || "",
            Maxlotsize: plant.Maxlotsize || "",
            FixedLot: plant.FixedLot || "",
            ReorderPt: plant.ReorderPt || "",
            MaxStock: plant.MaxStock || "",
            RoundVal: plant.RoundVal || "",
            AssyScarp:plant.AssyScarp || "",
            Spproctype: plant.Spproctype || "",
            SlocExprc: plant.SlocExprc || "",
            Inhseprodt: plant.Inhseprodt || "",
            PlndDelry: plant.PlndDelry || "",
            GrPrTime: plant.GrPrTime || "",
            Safetytime: plant.Safetytime || "",
            SafetyStk: plant.SafetyStk || "",
            BwdCons: plant.BwdCons || "",
            FwdCons: plant.FwdCons || "",
            Replentime: plant.Replentime || "",
            PlTiFnce: plant.PlTiFnce || "",
            IssStLoc: plant.IssStLoc || "",
            AssyScrap: plant.AssyScrap || "",
          };
          return acc;
        }, {}),
        "Work Scheduling" : (item.Toplantdata || []).reduce((acc, plant) => {
          acc[`${plant?.Plant}`] = {
            Unlimited: plant.Unlimited || "",
            ProdProf: plant.ProdProf || "",
          };
          return acc;
        }, {}),
        Sales: (item.Tosalesdata || []).reduce((acc, sale, index) => {
          const key = `${sale?.SalesOrg}-${sale?.DistrChan}`;
          acc[key] = {
            ...sale,
            SalStatus: sale.SalStatus || "",
            SalesId: sale.SalesId || null,
            ValidFrom: convertSAPDateForCalendar(sale.ValidFrom) || null,
            DelyUom: sale.DelyUom || "",
            DelygPlnt : sale.DelygPlnt || "",
            MatPrGrp: sale.MatPrGrp || "",
          };
          return acc;
        }, {}),
        "Sales-General": {
          "Sales-General" : {
            TransGrp : item?.Toclientdata?.TransGrp || "",
            BatchMgmt : item?.Toclientdata?.BatchMgmt || false,
            XSalStatus : item?.Toclientdata?.XSalStatus || '',
            Svalidfrom: convertSAPDateForCalendar(item?.Toclientdata?.Svalidfrom) || "",
          }
        },
        "Purchasing-General": {
          "Purchasing-General" : {
            VarOrdUn : item?.Toclientdata?.VarOrdUn || "",
            PurValkey : item?.Toclientdata?.PurValkey || "",
          }
        },
        TaxData: {
          TaxData: {
            TaxDataSet: (item.Tocontroldata || []).reduce((acc, controlData) => {
              const country = controlData.Depcountry;
              const controlId = controlData.ControlId;      
              Object.keys(controlData)
                .filter((key) => key.startsWith("TaxType"))
                .forEach((taxKey, index) => {
                  const taxType = controlData[taxKey];
                  const taxClass = controlData[`Taxclass${index + 1}`];  
                  if (taxType && taxClass !== undefined) {
                    let existingEntry = acc.find(
                      (entry) => entry.Country === country && entry.TaxType === taxType
                    );
        
                    const selected = {
                      TaxClass: taxClass.toString(),
                    };
        
                    if (!existingEntry) {
                      acc.push({
                        Country: country,
                        TaxType: taxType,
                        ControlId: controlId,
                        SelectedTaxClass: selected,
                      });
                    } else {
                      existingEntry.SelectedTaxClass = selected;
                    }
                  }
                });
              return acc;            
            }, []),
          },
        },       
        [MATERIAL_VIEWS.SALES_PLANT]: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            ...plant,
            Matfrgtgrp: plant.Matfrgtgrp || "",
            ProfitCtr: plant.ProfitCtr || "",
            Loadinggrp: plant.Loadinggrp || "",
            Availcheck: plant.Availcheck || "",
          };
          return acc;
        }, {}),
        [MATERIAL_VIEWS.WAREHOUSE]: (item.Towarehousedata || []).reduce((acc, warehouse,index) => {
          acc[warehouse?.WhseNo] = {
            ...warehouse,
            Placement: warehouse.Placement || "",
          };
          return acc;
        }, {}),
        "Tostroragelocationdata": item.Tostroragelocationdata || [],
        [MATERIAL_VIEWS.BOM]: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            // ...plant,
            BomUsage: plant.BomUsage || "",
            AltBom: plant.AltBom || "",
            Category: plant.Category || "",
            Component: plant.Component || "",
            Quantity: plant.Quantity || "",
            CompUom: plant.CompUom || "",
            Bvalidfrom: convertSAPDateForCalendar(plant.Bvalidfrom) || "",
            Bvalidto: convertSAPDateForCalendar(plant.Bvalidto) || "",
          };
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SOURCE_LIST]: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            // ...plant,
            PurchaseOrg: plant.PurchaseOrg || "",
            ProcurementPlant: plant.ProcurementPlant || "",
            SOrderUnit: plant.SOrderUnit || "",
            Agreement: plant.Agreement || "",
            AgreementItem: plant.AgreementItem || "",
            FixedSupplySource: plant.FixedSupplySource || "",
            Blocked : plant?.Blocked || "",
            SMrp : plant?.SMrp || "",
            Slvalidfrom: convertSAPDateForCalendar(plant.Slvalidfrom) || "",
            Slvalidto: convertSAPDateForCalendar(plant.Slvalidto) || "",
          };
          return acc;
        }, {}),
        [MATERIAL_VIEWS.STORAGE] : (item.Tostroragelocationdata || []).reduce((acc, storage) => {
          const key = `${storage?.Plant}-${storage?.StgeLoc}`;
          acc[key] = {
            ...storage,
          };
          return acc;
        }, {}),
        [MATERIAL_VIEWS.STORAGE_PLANT]: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            CcPhInv : plant?.CcPhInv || "",
            CcFixed : plant?.CcFixed || "",
            // MaxStock : plant?.MaxStock || "", already there in mrpView
            StgePdUn : plant?.StgePdUn || "",
            DefaultStockSegment : plant?.DefaultStockSegment || "",
            NegStocks : plant?.NegStocks === true || plant?.NegStocks === "X" || plant?.NegStocks === "TRUE" ? "X" : "", 
            SernoProf : plant?.SernoProf || "",
            // Loadinggrp : plant?.Loadinggrp || "", already there in salesPlantView
            DistrProf : plant?.DistrProf || "",
            DetermGrp : plant?.DetermGrp || "",
            IuidRelevant : plant?.IuidRelevant === true || plant?.IuidRelevant === "X" || plant?.IuidRelevant === "TRUE" ? "X" : "",
            UidIea : plant?.UidIea || "",
            IuidType : plant?.IuidType || "",
            SortStockBasedOnSegment : plant?.SortStockBasedOnSegment || "",
          };
          return acc;
        }, {}),
        [MATERIAL_VIEWS.STORAGE_GENERAL] : {
          [MATERIAL_VIEWS.STORAGE_GENERAL] : {
            HazMatNo : item?.Toclientdata?.HazMatNo || "",
            QtyGrGi : item?.Toclientdata?.QtyGrGi || "",
            TempConds : item?.Toclientdata?.TempConds || "",
            Container : item?.Toclientdata?.Container || "",
            LabelType : item?.Toclientdata?.LabelType || "",
            LabelForm  : item?.Toclientdata?.LabelForm || "",
            AppdBRec : item?.Toclientdata?.AppdBRec || "",
            Minremlife : item?.Toclientdata?.Minremlife || "",
            ShelfLife : item?.Toclientdata?.ShelfLife || "",
            PeriodIndExpirationDate : item?.Toclientdata?.PeriodIndExpirationDate || "",
            RoundUpRuleExpirationDate : item?.Toclientdata?.RoundUpRuleExpirationDate || "",
            StorPct : item?.Toclientdata?.StorPct || "",
            SledBbd : item?.Toclientdata?.SledBbd || "",
            SerializationLevel : item?.Toclientdata?.SerializationLevel || "",
            MaturationTime : item?.Toclientdata?.MaturationTime || "",
            ReqMaxShLife : item?.Toclientdata?.ReqMaxShLife || "",
          }
        },
      },
      ManufacturerID: item?.ManufacturerId || "",
      unitsOfMeasureData:
        (item?.Touomdata || []).length === 0
          ? []
          : item?.Touomdata.map((uom, index) => ({
            ...uom,
            id: uom?.UomId,  
            UomId: uom?.UomId || null,
            xValue: uom?.Denominatr || "1",  
            aUnit: uom?.AltUnit || "", 
            measureUnitText: "",  
            yValue: uom?.Numerator || "1",    
            eanUpc: uom?.EanUpc || "",  
            eanCategory: uom?.EanCat || "", 
            length: uom?.Length, 
            width: uom?.Width,  
            height: uom?.Height,  
            unitsOfDimension: uom?.UnitDim || "",  
            volume: uom?.Volume || "",  
            volumeUnit: uom?.Volumeunit || "",  
            grossWeight: uom?.GrossWt || "", 
            netWeight:  uom?.NetWeight || "",  
            weightUnit: uom?.UnitOfWt || "", 
            })),
      eanData:
        (item?.Toeandata || []).length === 0
          ? []
          : item?.Toeandata.map((ean, index) => ({
            ...ean,
              id: ean?.EanId,
              EanId: ean?.EanId,
              altunit: ean?.Unit,
              eanUpc: ean?.EanUpc,
              eanCategory: ean?.EanCat,
              MainEan: ean?.MainEan,
              au: ean?.Au,
            })),
      additionalData:
        (item?.Tomaterialdescription || []).length === 0
          ? []
          : (item?.Tomaterialdescription || []).map((addData,index) => ({
              id: index+1,
              MaterialDescriptionId: addData?.MaterialDescriptionId,
              Function: addData?.Function,
              Material: addData?.Material,
              language: addData?.Langu,
              materialDescription: addData?.MatlDesc,
              DelFlag: addData?.DelFlag,
            })),
      Tomaterialerrordata: item?.Tomaterialerrordata || {},
      changeLogData: item?.changeLogData || {},
      Tochildrequestheaderdata: item?.Tochildrequestheaderdata ? item?.Tochildrequestheaderdata : {}
    };
  });
  return { payload };
  
};

export const transformApiResponseToReduxPayloadExtend = (apiResponse,orgRow,initialPayload,selectedViews,selectedMaterialRow) => {
  let counter = 0;
  const nextLineNumber = () => {
    counter += 10;
    return counter;
  };

  const payload = {
    payloadData: {
      ...initialPayload
    },
    errorFields: [],
    requiredFields: [],
    additionalData: [],
    unitsOfMeasureData: [],
    taxData: [],
    requiredFieldsGI: ["BusinessJustification"],
    singleMatPayload: {
      BusinessJustification: "",
      ChoosePriorityLevel: "Medium",
    },
    generalInformation: [],
    mandatoryFields: [],
    fcRows:[],
    errorData: {},
    changeFieldRows:[],
    changeFieldRowsDisplay:{},
    requestorPayload: {},
    changeLogData:{},
    filteredButtons:[],
    dynamicKeyValues: {},
    dataLoading: false,
    isSubmitDisabled: true,
    newRowIds: [],
    selectedRows: [],
    unselectedRows: [],
    templateArray: [],
    whseList: [],
    matNoList: [],
  };

  function getAccountingId(accountingData) {
    return accountingData?.map((item) => item?.AccountingId);
  }

  apiResponse.forEach((item) => {
    const dynamicKey = item.Material.toString();
    const purchasingKey = [item.Toplantdata]
    const accountingKey = [item.Toaccountingdata] ?? []
    const wareHouseKey = [item.ToWarehousedata] ?? []
    const plantID = orgRow?.map((item) => item?.plant?.value?.code);
    const salesID = orgRow?.map((item) => `${item.salesOrg?.code}-${item.dc?.value?.code}`);
    const wareHouseID = orgRow?.map((item) => item?.warehouse?.value?.code);
    payload[dynamicKey] = {
      headerData: {
        id: dynamicKey,
        clientId: item?.ToBasicdata?.ClientId || null,
        AccountingId: getAccountingId(item?.Toaccountingdata) || null,
        included: true,
        industrySector: {
          code: selectedMaterialRow?.IndSector || "",
        },
        materialType: {
          code: selectedMaterialRow?.MatlType || "",
        },
        lineNumber: selectedMaterialRow.lineNumber ?? nextLineNumber() ?? null,
        materialNumber: selectedMaterialRow.Material || "",
        globalMaterialDescription: selectedMaterialRow.globalMaterialDescription || "",
        views: selectedViews,
        validated: VALIDATION_STATUS.default,
        orgData: orgRow || [],
      },
      payloadData: {
        "Basic Data": {
          basic: {
            BaseUom: item?.ToBasicdata?.BaseUom || "",
            MatlGroup: item?.ToBasicdata?.MatlGroup || "",
            MatGroupPackagingMat: item?.ToBasicdata?.MatGroupPackagingMat || "",
            Division:  item?.ToBasicdata?.Division || "",
            ProdHier: item?.ToBasicdata?.ProdHier || "",
            VolumeUnit: item?.ToBasicdata?.VolumeUnit || "",
            UnitOfWt: item?.ToBasicdata?.UnitOfWt || "",
            GItemCat: item?.ToBasicdata?.GItemCat || "",
            BasicMatl: item?.ToBasicdata?.BasicMatl || "",
            Hazmatprof: item?.ToBasicdata?.Hazmatprof || "",
            OldMatNo: item?.ToBasicdata?.OldMatNo || "",
            Extmatlgrp: item?.ToBasicdata?.Extmatlgrp || "",
            DsnOffice: item?.ToBasicdata?.DsnOffice || "",
            PurValkey: item?.ToBasicdata?.PurValkey || "",
            NetWeight: item?.ToBasicdata?.NetWeight || "",
            HighVisc: item?.ToBasicdata?.HighVisc || "",
            IndSector: item?.ToBasicdata?.IndSector || "",
            PurStatus: item?.ToBasicdata?.PurStatus || "",
            HazMatNo: item?.ToBasicdata?.HazMatNo || "",
            StdDescr: item?.ToBasicdata?.StdDescr || "",
            ShelfLife: item?.ToBasicdata?.ShelfLife || "",
            ProdAlloc: item?.ToBasicdata?.ProdAlloc || "",
            CSalStatus: item?.ToBasicdata?.CSalStatus || "",
            IntlPoPrice: item?.ToBasicdata?.IntlPoPrice || "",
            PryVendor : item?.ToBasicdata?.PryVendor || "",
            PlanningArea : item?.ToBasicdata?.PlanningArea || "",
            PlanningFactor : item?.ToBasicdata?.PlanningFactor || "",
            ReturnMatNumber : item?.ToBasicdata?.ReturnMatNumber || "",
            ParentMatNumber : item?.ToBasicdata?.ParentMatNumber || "",
            DiversionControlFlag : item?.ToBasicdata?.DiversionControlFlag || "",
            Pvalidfrom: convertSAPDateForCalendar(item?.ToBasicdata?.Pvalidfrom) || "",
          },
        },
        Purchasing: (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
            Plant:id,
            PurValkey: item?.ToBasicdata?.PurValkey  || "",
            PurGroup: plant.PurGroup || "",
            PurStatus: plant.PurStatus || "",
            PlantId: plant?.PlantId || '',
            CommCode: plant.CommCode || "",
            CommCoUn:plant.CommCoUn || "",
            Countryori: plant.Countryori || "",
            IndPostToInspStock: plant.IndPostToInspStock || "",
            VarOrdUn : item?.ToBasicdata?.VarOrdUn || "",
            HtsCode: plant?.HtsCode || "",
          };
        });
          return acc;
        }, {}),
        Accounting: (Array.isArray(accountingKey) && accountingKey.length > 0 
          ? accountingKey 
          : [{}]
        ).reduce((acc, accounting) => {
          plantID.forEach((id) => {
            const accountingData = accounting[0] || {};
            acc[id] = {
              ...accountingData,
              ValClass: accountingData?.ValClass || "",
              PriceCtrl: accountingData?.PriceCtrl || "",
              PriceUnit: accountingData?.PriceUnit || "",
              StdPrice: accountingData?.StdPrice || "",
            };
          });
          return acc;
        }, {}),

        Costing: (Array.isArray(accountingKey) && accountingKey.length > 0
          ? accountingKey
          : [{}]
        ).reduce((acc, accounting) => {
          plantID.forEach((id) => {
            const accountingData = accounting[0] || {};
            acc[id] = {
              ...accountingData,
              OrigMat: accountingData?.OrigMat || "",
              LotSize: item?.Toplantdata?.LotSize || "",
              ProfitCtr: item?.Toplantdata?.ProfitCtr || "",
              VarianceKey: item?.Toplantdata?.VarianceKey || "",
              MovingPr: accountingData?.MovingPr || "",
              StdPrice: accountingData?.StdPrice || "",
            };
          });
          return acc;
        }, {}),
        MRP: (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
            ...plant,
            MrpType: plant.MrpType || "",
            MrpCtrler: plant.MrpCtrler || "",
            ProcType: plant.ProcType || "",
            Backflush: plant.Backflush || "",
            PeriodInd: plant.PeriodInd || "",
            PlanStrgp: plant.PlanStrgp || "",
            Consummode: plant.Consummode || "",
            Availcheck: plant.Availcheck || "",
            DepReqId: plant.DepReqId || "",
            Lotsizekey: plant.Lotsizekey || "",
            SaftyTId: plant.SaftyTId || "",
            SmKey: plant.SmKey || "",
            MixedMrp: plant.MixedMrp || "",
            GrpReqmts: plant.GrpReqmts || "",
            Minlotsize: plant.Minlotsize || "",
            Maxlotsize: plant.Maxlotsize || "",
            FixedLot: plant.FixedLot || "",
            ReorderPt: plant.ReorderPt || "",
            MaxStock: plant.MaxStock || "",
            RoundVal: plant.RoundVal || "",
            AssyScarp:plant.AssyScarp || "",
            Spproctype: plant.Spproctype || "",
            SlocExprc: plant.SlocExprc || "",
            Inhseprodt: plant.Inhseprodt || "",
            PlndDelry: plant.PlndDelry || "",
            GrPrTime: plant.GrPrTime || "",
            Safetytime: plant.Safetytime || "",
            SafetyStk: plant.SafetyStk || "",
            BwdCons: plant.BwdCons || "",
            FwdCons: plant.FwdCons || "",
            Replentime: plant.Replentime || "",
            PlTiFnce: plant.PlTiFnce || "",
            IssStLoc: plant.IssStLoc || "",
            AssyScrap: plant.AssyScrap || "",
          };
        });
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SALES_PLANT]: (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
            Matfrgtgrp: plant.Matfrgtgrp || "",
            ProfitCtr: item?.Toplantdata?.ProfitCtr || "",
            Loadinggrp: item?.Toplantdata?.Loadinggrp || "",
            Availcheck: plant.Availcheck || "",
          };
        });
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SALES_GENERAL]: {
          [MATERIAL_VIEWS.SALES_GENERAL]: {
            TransGrp : item?.ToBasicdata?.TransGrp || "",
            BatchMgmt : item?.ToBasicdata?.BatchMgmt || false,
            XSalStatus : item?.ToBasicdata?.XSalStatus || '',
            Svalidfrom: convertSAPDateForCalendar(item?.Toclientdata?.Svalidfrom) || "",
          }
        },
        [MATERIAL_VIEWS.PURCHASING_GENERAL]: {
          [MATERIAL_VIEWS.PURCHASING_GENERAL] : {
            VarOrdUn : item?.ToBasicdata?.VarOrdUn || "",
            PurValkey : item?.ToBasicdata?.PurValkey || "",
          }
        },
        TaxData: {
          TaxData: {
            UniqueTaxDataSet: (item.Tocontroldata || []).reduce((acc, controlData) => {
              const country = controlData.Depcountry;
              const ControlId = controlData.ControlId;
              Object.keys(controlData)
                .filter((key) => key.startsWith("TaxType"))
                .forEach((taxKey, index) => {
                  const taxType = controlData[taxKey];
                  const taxClass = controlData[`Taxclass${index + 1}`];

                  if (taxType && taxClass !== undefined) {
                    let existingEntry = acc.find((entry) => entry.Country === country && entry.TaxType === taxType);

                    if (!existingEntry) {
                      existingEntry = {
                        Country: country,
                        TaxType: taxType,
                        ControlId: ControlId,
                        TaxClasses: [],
                        SelectedTaxClass: {
                          TaxClass: taxClass,
                          TaxClassDesc: `Description for ${taxClass}`,
                        },
                      };
                      acc.push(existingEntry);
                    }

                    existingEntry.SelectedTaxClass = {
                      TaxClass: taxClass,
                      TaxClassDesc: `Description for ${taxClass}`,
                    };
                  }
                });

              return acc;
            }, []),
          },
        },
        [MATERIAL_VIEWS.WORKSCHEDULING] : (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
              Unlimited: plant.Unlimited || "",
              ProdProf: plant.ProdProf || "",
            };
          });
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SALES]: ([item.Tosalesdata] || []).reduce((acc, sale) => {
          salesID.forEach((id) => {
            acc[id] = {
            ...sale,
            SalStatus: sale.SalStatus || "",
            SalesId: sale.SalesId,
            MatPrGrp: sale.MatPrGrp || "",
            DelygPlnt: sale.DelygPlnt || "",
            RebateGrp: sale.RebateGrp || "",
            DelyUom: sale.DelyUom || "",
            ValidFrom: convertSAPDateForCalendar(sale.ValidFrom) || null,
          };
        });
          return acc;
        }, {}),
        [MATERIAL_VIEWS.WAREHOUSE]: (wareHouseKey || []).reduce((acc, warehouse) => {
          wareHouseID.forEach((id) => {
            acc[id] = {
            ...warehouse,
            Placement: warehouse.Placement || "",
          };
        });
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SALES_PLANT]: (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
            Matfrgtgrp: plant.Matfrgtgrp || "",
            ProfitCtr: plant.ProfitCtr || "",
            Loadinggrp: plant.Loadinggrp || "",
            Availcheck: plant.Availcheck || "",
          };
        });
          return acc;
        }, {}),
      },
      
      unitsOfMeasureData:
        (item?.Touomdata || []).length === 0
          ? []
          : item?.Touomdata.map((uom, index) => ({
              ...uom,
              id: index,
              UomId: uom?.UomId,
              xValue: uom?.Denominatr || "",
              aUnit: uom?.AltUnit || "",
              measureUnitText: uom?.measureUnitText || "",
              yValue: uom?.Numerator || "",
              bUnit: uom?.bUnit || "",
              measurementUnitText: uom?.measurementUnitText|| "",
              eanUpc: uom?.EanUpc || "",
              eanCategory: uom?.EanCat|| "",
              autoCheckDigit: uom?.autoCheckDigit || "",
              addEans: uom?.addEans || "",
              length: uom?.Length || "",
              width: uom?.Width || "",
              height: uom?.Height || "",
              unitsOfDimension: uom?.UnitDim || "", 
              volume: uom?.Volume || "",
              volumeUnit: uom?.Volumeunit || "",
              grossWeight: uom?.GrossWt || "", 
              netWeight: uom?.NetWeight || item.ToBasicdata.NetWeight || "",  
              weightUnit: uom?.UnitOfWt || "", 
              noLowerLvlUnits: uom?.noLowerLvlUnits || "",
              lowerLvlUnits: uom?.lowerLvlUnits || "",
              remVolAfterNesting: uom?.remVolAfterNesting || "",
              maxStackFactor: uom?.maxStackFactor || "",
              maxTopLoadFullPkg: uom?.maxTopLoadFullPkg || "",
              UomToploadFullPkg: uom?.UomToploadFullPkg || "",
              capacityUsage: uom?.CapacityUsage || "",
              UomCategory: uom?.UomCategory || "",
            })),
      additionalData:
        (item?.Tomaterialdescription || []).length === 0
          ? []
          : (item?.Tomaterialdescription || []).map((addData,index) => ({   
              id: index,
              MaterialDescriptionId: addData?.MaterialDescriptionId,
              Function: addData?.Function,
              Material: addData?.Material,
              language: addData?.Langu,
              materialDescription: addData?.MatlDesc,
              DelFlag: addData?.DelFlag,
            })),
            Tomaterialerrordata:item?.Tomaterialerrordata || {}
    };
  });
  let filteredPayload = {};
  Object.keys(payload).forEach((key) => {
    if (key.includes("-") || /\d/.test(key)) {
      filteredPayload[key] = payload[key];
    }
  });
  
  return filteredPayload;
};
export const transformResponseForCreateRef = (apiResponse,initialPayload={}) => {
  let region=initialPayload?.Region;
  let copyPayload = {}
  apiResponse.forEach((item) => {
    copyPayload = {
      payloadData: {
        "Basic Data": {
          basic: {
            BaseUom: item?.ToBasicdata?.BaseUom || "",
            MatlGroup: item?.ToBasicdata?.MatlGroup || "",
            Division: item?.Torequestheaderdata?.Division || item?.ToBasicdata?.Division || "",
            ProdHier: item?.ToBasicdata?.ProdHier || "",
            VolumeUnit: item?.ToBasicdata?.VolumeUnit || "",
            UnitOfWt: item?.ToBasicdata?.UnitOfWt || "",
            GItemCat: item?.ToBasicdata?.GItemCat || "",
            BasicMatl: item?.ToBasicdata?.BasicMatl || "",
            Hazmatprof: item?.ToBasicdata?.Hazmatprof || "",
            OldMatNo: item?.ToBasicdata?.OldMatNo || "",
            Extmatlgrp: item?.ToBasicdata?.Extmatlgrp || "",
            DsnOffice: item?.ToBasicdata?.DsnOffice || "",
            NetWeight: item?.ToBasicdata?.NetWeight || "",
            HighVisc: item?.ToBasicdata?.HighVisc || "",
            IndSector: item?.ToBasicdata?.IndSector || "",
            HazMatNo: item?.ToBasicdata?.HazMatNo || "",
            StdDescr: item?.ToBasicdata?.StdDescr || "",
            ShelfLife: item?.ToBasicdata?.ShelfLife || "",
            CSalStatus : item?.ToBasicdata?.CSalStatus || "",
            IntlPoPrice : item?.ToBasicdata?.IntlPoPrice || "",
            PryVendor : item?.ToBasicdata?.PryVendor || "",
            PlanningArea : item?.ToBasicdata?.PlanningArea || "",
            PlanningFactor : item?.ToBasicdata?.PlanningFactor || "",
            ReturnMatNumber : item?.ToBasicdata?.ReturnMatNumber || "",
            ParentMatNumber : item?.ToBasicdata?.ParentMatNumber || "",
            DiversionControlFlag : item?.ToBasicdata?.DiversionControlFlag || "",
            MatGroupPackagingMat : item?.ToBasicdata?.MatGroupPackagingMat || "",
            ProdAlloc: item?.ToBasicdata?.ProdAlloc || "",
            Pvalidfrom: convertSAPDateForCalendar(item?.ToBasicdata?.Pvalidfrom) || "",
          },
        },
        ManufacturerID: item?.ManufacturerId || "",
        Purchasing: item?.Toplantdata ? {
          PurGroup: item?.Toplantdata?.PurGroup || "",
          PurStatus: item?.Toplantdata?.PurStatus || "",
          PlantId: item?.Toplantdata?.PlantId,
          CommCode: item?.Toplantdata?.CommCode || "",
          CommCoUn:item?.Toplantdata?.CommCoUn || "",
          Countryori: item?.Toplantdata?.Countryori || "",
          IndPostToInspStock: item?.Toplantdata?.IndPostToInspStock || "",
          HtsCode: item?.Toplantdata?.HtsCode || "",
        } : {},
        Accounting : item?.Toaccountingdata?.length ? {
          AccountingId: item?.Toaccountingdata[0]?.AccountingId || "",
          ValClass : item?.Toaccountingdata[0]?.ValClass || '',
          PriceCtrl : item?.Toaccountingdata[0]?.PriceCtrl || '',
          PriceUnit : item?.Toaccountingdata[0]?.PriceUnit || '',
          StdPrice : item?.Toaccountingdata[0]?.StdPrice || ''
        } : {},
        Costing : item.Toaccountingdata?.length ? {
          AccountingId: item.Toaccountingdata[0]?.AccountingId || "",
          OrigMat : item.Toaccountingdata[0]?.OrigMat || "",
          LotSize : item.Toplantdata?.LotSize || '',
          VarianceKey : item.Toplantdata?.VarianceKey || '',
          MovingPr : item.Toaccountingdata[0]?.MovingPr || '',
          StdPrice : item.Toaccountingdata[0]?.StdPrice || '',
          ProfitCtr: item.Toplantdata?.ProfitCtr
        } : {},
        MRP: item.Toplantdata ? {
          MrpType: item.Toplantdata?.MrpType || "",
          IssStLoc: item.Toplantdata?.IssStLoc || "",
          MrpCtrler: item.Toplantdata?.MrpCtrler || "",
          ProcType: item.Toplantdata?.ProcType || "",
          Backflush: item.Toplantdata?.Backflush || "",
          PeriodInd: item.Toplantdata?.PeriodInd || "",
          PlanStrgp: item.Toplantdata?.PlanStrgp || "",
          Consummode: item.Toplantdata?.Consummode || "",
          Availcheck: item.Toplantdata?.Availcheck || "",
          DepReqId: item.Toplantdata?.DepReqId || "",
          Lotsizekey: item.Toplantdata?.Lotsizekey || "",
          SaftyTId: item.Toplantdata?.SaftyTId || "",
          SmKey: item.Toplantdata?.SmKey || "",
          MixedMrp: item.Toplantdata?.MixedMrp || "",
          GrpReqmts: item.Toplantdata?.GrpReqmts || "",
          Minlotsize: item.Toplantdata?.Minlotsize || "",
          Maxlotsize: item.Toplantdata?.Maxlotsize || "",
          FixedLot: item.Toplantdata?.FixedLot || "",
          ReorderPt: item.Toplantdata?.ReorderPt || "",
          MaxStock: item.Toplantdata?.MaxStock || "",
          RoundVal: item.Toplantdata?.RoundVal || "",
          AssyScarp:item.Toplantdata?.AssyScarp || "",
          Spproctype: item.Toplantdata?.Spproctype || "",
          SlocExprc: item.Toplantdata?.SlocExprc || "",
          Inhseprodt: item.Toplantdata?.Inhseprodt || "",
          PlndDelry: item.Toplantdata?.PlndDelry || "",
          GrPrTime: item.Toplantdata?.GrPrTime || "",
          Safetytime: item.Toplantdata?.Safetytime || "",
          SafetyStk: item.Toplantdata?.SafetyStk || "",
          BwdCons: item.Toplantdata?.BwdCons || "",
          FwdCons: item.Toplantdata?.FwdCons || "",
          Replentime: item.Toplantdata?.Replentime || "",
          PlTiFnce: item.Toplantdata?.PlTiFnce || "",
          AssyScrap : item.Toplantdata?.AssyScrap || "",
        } : {},
        "Work Scheduling" : item.Toplantdata ? {
            Unlimited: item.Toplantdata?.Unlimited || "",
            ProdProf: item.Toplantdata?.ProdProf || "",
          } : {},
        Sales: item.Tosalesdata ? {
            ...item.Tosalesdata,
            SalStatus: item.Tosalesdata.SalStatus || "",
            SalesId: item.Tosalesdata.SalesId,
            MatPrGrp: item.Tosalesdata.MatPrGrp || "",
            ValidFrom: convertSAPDateForCalendar(item.Tosalesdata.ValidFrom) || null,
          } : {},
        "Sales-General": {
          "Sales-General" : {
            TransGrp : item?.ToBasicdata?.TransGrp || "",
            BatchMgmt : item?.ToBasicdata?.BatchMgmt || false,
            XSalStatus : item?.ToBasicdata?.XSalStatus || '',
            Svalidfrom : convertSAPDateForCalendar(item?.ToBasicdata?.Svalidfrom) || null,
          }
        },
        "Purchasing-General": {
          "Purchasing-General" : {
            VarOrdUn : item?.ToBasicdata?.VarOrdUn || "",
            PurValkey : item?.ToBasicdata?.PurValkey || "",
          }
        },
        TaxData: {
          TaxData: {
            UniqueTaxDataSet: (item.Tocontroldata || []).reduce((acc, controlData) => {
              const country = controlData.Depcountry;
              const ControlId = controlData.ControlId;
              Object.keys(controlData)
                .filter((key) => key.startsWith("TaxType"))
                .forEach((taxKey, index) => {
                  const taxType = controlData[taxKey];
                  const taxClass = controlData[`Taxclass${index + 1}`];

                  if (taxType && taxClass !== undefined) {
                    let existingEntry = acc.find((entry) => entry.Country === country && entry.TaxType === taxType);

                    if (!existingEntry) {
                      existingEntry = {
                        Country: country,
                        TaxType: taxType,
                        ControlId: ControlId,
                        TaxClasses: [],
                        SelectedTaxClass: {
                          TaxClass: taxClass,
                          TaxClassDesc: `Description for ${taxClass}`,
                        },
                      };
                      acc.push(existingEntry);
                    }

                    existingEntry.SelectedTaxClass = {
                      TaxClass: taxClass,
                      TaxClassDesc: `Description for ${taxClass}`,
                    };
                  }
                });

              return acc;
            }, []),
          },
        },
        [MATERIAL_VIEWS.SALES_PLANT]: item.Toplantdata ? {
            Matfrgtgrp: item.Toplantdata?.Matfrgtgrp || "",
            ProfitCtr: item.Toplantdata?.ProfitCtr || "",
            Loadinggrp: item.Toplantdata?.Loadinggrp || "",
            Availcheck: item.Toplantdata?.Availcheck || "",
          } : {},
        [MATERIAL_VIEWS.WAREHOUSE]: item.ToWarehousedata ? {
            ...item.ToWarehousedata
          } : {},
        [MATERIAL_VIEWS.STORAGE]: item.Tostroragelocationdata ? {
            ...item.Tostroragelocationdata
        } : {},
        [MATERIAL_VIEWS.STORAGE_PLANT]: item?.Toplantdata ? {
          CcPhInv: item?.Toplantdata?.CcPhInv || "",
          CcFixed: item?.Toplantdata?.CcFixed || "",
          // MaxStock : item?.Toplantdata?.MaxStock || "", already there in mrpView
          StgePdUn : item?.Toplantdata?.StgePdUn || "",
          DefaultStockSegment : item?.Toplantdata?.DefaultStockSegment || "",
          NegStocks : item?.Toplantdata?.NegStocks || false, 
          SernoProf : item?.Toplantdata?.SernoProf || "",
          // Loadinggrp : item?.Toplantdata?.Loadinggrp || "", already there in salesPlantView
          DistrProf : item?.Toplantdata?.DistrProf || "",
          DetermGrp : item?.Toplantdata?.DetermGrp || "",
          IuidRelevant : item?.Toplantdata?.IuidRelevant || false,
          UidIea : item?.Toplantdata?.UidIea || "",
          IuidType: item?.Toplantdata?.IuidType || "",
        } : {},
        [MATERIAL_VIEWS.STORAGE_GENERAL] : {
          [MATERIAL_VIEWS.STORAGE_GENERAL] : {
            HazMatNo : item?.ToBasicdata?.HazMatNo || "",
            QtyGrGi : item?.ToBasicdata?.QtyGrGi || "",
            TempConds : item?.ToBasicdata?.TempConds || "",
            Container : item?.ToBasicdata?.Container || "",
            LabelType : item?.ToBasicdata?.LabelType || "",
            LabelForm  : item?.ToBasicdata?.LabelForm || "",
            AppdBRec : item?.ToBasicdata?.AppdBRec || "",
            Minremlife : item?.ToBasicdata?.Minremlife || "",
            ShelfLife : item?.ToBasicdata?.ShelfLife || "",
            PeriodIndExpirationDate : item?.ToBasicdata?.PeriodIndExpirationDate || "",
            RoundUpRuleExpirationDate : item?.ToBasicdata?.RoundUpRuleExpirationDate || "",
            StorPct : item?.ToBasicdata?.StorPct || "",
            SledBbd : item?.ToBasicdata?.SledBbd || "",
            SerializationLevel : item?.ToBasicdata?.SerializationLevel || "",
            MaturationTime : item?.ToBasicdata?.MaturationTime || "",
            ReqMaxShLife : item?.ToBasicdata?.ReqMaxShLife || "",
          }
        },
      },
      //will remove after testing this mapping is not required 
      // eanData:
      //   (item?.Toeandata || []).length === 0
      //     ? []
      //     : item?.Toeandata.map((ean, index) => ({
      //       ...ean,
      //         id: ean?.EanId,
      //         EanId: ean?.EanId,
      //         altunit: ean?.Unit,
      //         eanUpc: ean?.EanUpc,
      //         eanCategory: ean?.EanCat,
      //         MainEan: ean?.MainEan,
      //       })),
      unitsOfMeasureData:
        (item?.Touomdata || []).length === 0
          ? []
          : item?.Touomdata.map((uom, index) => {
              const baseData = {
                ...uom,
                id: uom?.UomId,
                UomId: uom?.UomId,
                xValue: uom?.Denominatr,
                aUnit: uom?.AltUnit,
                measureUnitText: uom?.measureUnitText,
                yValue: uom?.Numerator,
                bUnit: uom?.bUnit,
                measurementUnitText: uom?.measurementUnitText,
                eanCategory: region ===  REGION_CODE?.US? uom?.EanCat || '':"",
                eanUpc: region ===  REGION_CODE?.US && uom?.EanCat === EAN_CATEGORIES?.MB ? ''|| uom?.EanUpc : '',
                autoCheckDigit: uom?.autoCheckDigit,
                addEans: uom?.addEans,
                unitsOfDimension: uom?.UnitDim,
                volumeUnit: uom?.Volumeunit || "",
                  
                weightUnit: uom?.UnitOfWt,
                noLowerLvlUnits: uom?.noLowerLvlUnits,
                lowerLvlUnits: uom?.lowerLvlUnits,
                remVolAfterNesting: uom?.remVolAfterNesting,
                maxStackFactor: uom?.maxStackFactor,
                maxTopLoadFullPkg: uom?.maxTopLoadFullPkg,
                UomToploadFullPkg: uom?.UomToploadFullPkg,
                capacityUsage: uom?.CapacityUsage,
                UomCategory: uom?.UomCategory,
                length: uom?.Length,
                width: uom?.Width,
                height: uom?.Height,
                volume: uom?.Volume,
                grossWeight: uom?.GrossWt
              };

              return baseData;
            }),
      additionalData:
        (item?.Tomaterialdescription || []).length === 0
          ? []
          : (item?.Tomaterialdescription || []).map((addData) => ({
              id: addData?.MaterialDescriptionId,
              MaterialDescriptionId: addData?.MaterialDescriptionId,
              Function: addData?.Function,
              Material: addData?.Material,
              language: addData?.Langu,
              materialDescription: addData?.MatlDesc,
              DelFlag: addData?.DelFlag,
            })),
    };
  });
  return { copyPayload };
  
};

export { getTimestamp, captureScreenShot, idGenerator, timeStampToDate, getColor_Status, getColor_priority, handleMultipleDownload, controller_UrlRedirecting, setStatusRecord, formValidator, capitalize, ASNidGenerator, getUserEmailId, checkIwaAccess, RETidGenerator, capitalizeByWord, multiConfirmationCheck, SESIDGenerator, PlanningMgmtIDGenerator, generateAccessList, ValidNavigateTo, updateTaskStatus, saveExcel, saveExcelMultiSheets, savePDF };
