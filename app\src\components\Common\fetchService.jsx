import { fetchEventSource } from "@microsoft/fetch-event-source";
import store from "../../app/store";

// ------------------------------------ START- AJAX OPERATIONS ---------------------------------------------//

export const doAjax = async (sUrl, sMethod = 'get', rSuccess, rError = () => { }, oPayload = {}, oHeaders) => {
  try {
    let appConfig = store.getState().applicationConfig
    let splitUrl = sUrl.split('/')
    const oParams = getParams(oHeaders, splitUrl[1]);
    sUrl = getUrl(sUrl, oParams);

    switch (sMethod.toLowerCase()) {
      case "deleteformdata":
        await fetch(sUrl, {
          method: "DELETE",
          body: oPayload,
          headers: {
            "Access-Control-Allow-Origin": "*",
            authorization: oParams.headers.Authorization
          },
          mode: "cors",
        })
          // .post(sUrl, oPayload, oParams)
          .then((res) => {
            // console.log(res.json())
            return res.json()
          })
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;

      case "postformdata":
        await fetch(sUrl, {
          method: "POST",
          body: oPayload,
          headers: {
            "Access-Control-Allow-Origin": "*",
            authorization: oParams.headers.Authorization
          },
          mode: "cors",
        })
          // .post(sUrl, oPayload, oParams)
          .then((res) => {
            // console.log(res.json())
            return res.json()
          })
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "post":
        await fetch(sUrl, {
          method: "POST",
          body: JSON.stringify(oPayload),
          headers: oParams.headers,
          mode: "cors",
        })
          // .post(sUrl, oPayload, oParams)
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "postblobfile": //POST Method call for blob type file upload
        await fetch(sUrl, {
          method: "POST",
          body: oPayload, // For file type we should not parse the body object to JSON.Stringify and no headers should be passed
          headers: { authorization: oParams.headers.Authorization },
        })
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "postandgetblob": //POST Method call for blob type file upload
        await fetch(sUrl, {
          method: "POST",
          body: JSON.stringify(oPayload),
          headers: oParams.headers,
          mode: "cors",
        })
          .then((res) => res.blob())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "get":
        await fetch(sUrl, {
          method: "GET",
          headers: oParams.headers,
          mode: "cors",
        })
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data)
          })
          .catch((err) => {
            rError(err)
          });
        break;
      case "getblobfile": //GET method for blob files
        await fetch(sUrl, {
          method: "GET",
          headers: oParams.headers,
          mode: "cors",
        })
          .then((res) => res.blob()) // return type should be in blob type
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "delete": //DELETE method call which does not accepts payload
        await fetch(sUrl, {
          method: "DELETE",
          headers: oParams.headers,
          mode: "cors",
        })
          // .delete(sUrl, oParams)
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "deletewithbody": //DELETE method call which accepts payload
        await fetch(sUrl, {
          method: "DELETE",
          headers: oParams.headers,
          body: JSON.stringify(oPayload),
          mode: "cors",
        })
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "put":
        await fetch(sUrl, {
          method: "PUT",
          headers: oParams.headers,
          mode: "cors",
          body: JSON.stringify(oPayload),
        })
          // .put(sUrl, oParams)
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "putformdata":
        await fetch(sUrl, {
          method: "PUT",
          headers: {
            "Access-Control-Allow-Origin": "*",
            authorization: oParams.headers.Authorization
          },
          mode: "cors",
          body: oPayload,
        })
          // .put(sUrl, oParams)
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;

      case "patch":
        await fetch(sUrl, {
          method: "PATCH",
          body: JSON.stringify(oPayload),
          headers: oParams.headers,
          mode: "cors",
        })
          // .post(sUrl, oPayload, oParams)
          .then((res) => res.json())
          .then((data) => {
            rSuccess(data);
          })
          .catch((err) => {
            rError(err);
          });
        break;
      case "eventstream":
        delete oParams.headers["Content-Type"]
        await fetchEventSource(sUrl, {
          method: "GET",
          headers: {
            Accept: "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            ...oParams.headers,
          },
          onopen(res) {
            //  console.log(res,'sseopen')
          },
          onmessage(event) {
            rSuccess(event)
          },
          onclose() {
            // console.log("Connection closed by the server");
          },
          onerror(err) {
            rError("Server Error", err);
          },
        });
      default:
        break;
    }
  } catch (e) {
    console.log(e)
  }

};
export const promiseAjax = (sUrl, sMethod, oPayload, oHeaders) => {
  const oParams = getParams(oHeaders);
  sUrl = getUrl(sUrl, oParams);

  switch (sMethod.toLowerCase()) {
    case "post":
      return fetch(sUrl, {
        method: "POST",
        body: JSON.stringify(oPayload),
        headers: oParams.headers,
        mode: "cors",
      });
    case "get":
      return fetch(sUrl, {
        method: "GET",
        headers: oParams.headers,
        mode: "cors",
      });
    case "delete":
      return fetch(sUrl, {
        method: "DELETE",
        headers: oParams.headers,
        mode: "cors",
      });
    case "put":
      return fetch(sUrl, {
        method: "PUT",
        headers: oParams.headers,
        mode: "cors",
      });
    case "patch":
      return fetch(sUrl, {
        method: "PATCH",
        body: JSON.stringify(oPayload),
        headers: oParams.headers,
        mode: "cors",
      });
    default:
      return;
  }
};

// export const doAjaxWithoutJson = async (sUrl, sMethod, oPayload, rSuccess, rError, oHeaders) => {
//   const oParams = getParams(oHeaders);
//   sUrl = getUrl(sUrl, oParams);
//   switch (sMethod.toLowerCase()) {
//     case "get":
//       await fetch(sUrl, {
//         method: "GET",
//         headers: oParams.headers,
//         mode: "cors",
//       })
//         // .get(sUrl, oParams)
//         .then((res) => res.text())
//         .then((data) => {
//           rSuccess(data);
//         })
//         .catch((err) => {
//           rError(err);
//         });
//       break;
//     default:
//     case "post":
//       await fetch(sUrl, {
//         method: "POST",
//         body: JSON.stringify(oPayload),
//         headers: oParams.headers,
//         mode: "cors",
//       })
//         .then((res) => res.text())
//         .then((data) => {
//           rSuccess(data);
//         })
//         .catch((err) => {
//           rError(err);
//         });
//       break;
//   }
// };
// // ------------------------------------ END - AJAX OPERATIONS ----------------------------------------------//

// // ------------------------------------ START - CRUD OPERATIONS --------------------------------------------//
// export const doCrudApiUpdate = async (query, args, rSuccess, rError) => {
//   const url = "/CrudApiServices/crud/api/updateQuery?converterName=map";
//   doAjax(
//     url,
//     "post",
//     { query: query, args: args },
//     function (data) {
//       rSuccess(data);
//     },
//     function (err) {
//       rError(err);
//     }
//   );
// };
// export const doCrudApi = async (query, args, rSuccess, rError) => {
//   const url = "/CrudApiServices/crud/api/fetchQuery?converterName=map";
//   doAjax(
//     url,
//     "post",
//     { query: query, args: args },
//     function (data) {
//       rSuccess(data);
//     },
//     function (err) {
//       rError(err);
//     }
//   );
// };
// ------------------------------------ END - CRUD OPERATIONS ------------------------------------------------//

// ------------------------------------ START - UTILITY FUNCTIONS --------------------------------------------//

const getParams = (oHeaders, sUrl) => {
  let appConfig = store.getState().applicationConfig

  let oParams = {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
    },
  };
  if ((appConfig?.environment === 'localhost' && sUrl !== "cw-scp-iwa-oauth2"&& sUrl !== "cw-caf-idm-services") ||
    (appConfig?.environment === '127.0.0.1' && sUrl !== "cw-scp-iwa-oauth2")) {
    oParams.headers = {
      ...oParams.headers,
      Authorization: appConfig?.environment === 'localhost' || appConfig?.environment === '127.0.0.1' 
        ? `bearer ********************************************************************************************************************************************************************************************************************************************************************************.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J28HyLPCjCjh-BqfZCIRAGvwyXVYQKkAT14f0IMGjEkp8qitjnRBcDINUqZGUZHJNlRYt9ZFeVHl0AT4MfHfHXQIDMAnqsaJPLiMDxgxoHnAlY85GRvc93nqQf44qZOavXUFhvidDrEdj5zFjfRU61d0Ej_Zl765dXBnat3Z2k2pn5YTn-mUuZM-r2ZM-ufC27qi1zZqTAprj4yEaw2LgVzdc8yGkr-HWIWty522C1rmlHdZ7jKR4p8gaeL6l3Z0zUDxLoO9YSxvgRSY6ndgWYNcD6BnvFC6B_28wMc1z33cj5O9Rs17IHsZzlAH_90XeU-a7XtJI-rGpla_fmJCfA`
        : `Bearer ${appConfig.token}`
    };
    
  } else if ((appConfig?.environment === 'localhost' && sUrl === "cw-scp-iwa-oauth2") ||
    (appConfig?.environment === '127.0.0.1' && sUrl === "cw-scp-iwa-oauth2")) {
    oParams.headers = {
      ...oParams.headers,
      Authorization: appConfig.iwaToken
    };
  }
  else if ((appConfig?.environment === 'localhost' && sUrl === "cw-caf-idm-services") ||
  (appConfig?.environment === '127.0.0.1' && sUrl === "cw-caf-idm-services")) {
  oParams.headers = {
    ...oParams.headers,
    Authorization: `Bearer ` + appConfig.idmToken
  };
}

  if (oHeaders && typeof oHeaders === 'object' && !Array.isArray(oHeaders)) {
    oParams.headers = {
      ...oParams.headers,
      ...oHeaders
    };
  }
  
  return oParams;
};
const getUrl = (sUrl, oParams) => {
  let appConfig = store.getState().applicationConfig

  //Remove the Service Call identifier with base url from .env file
  if ((appConfig?.environment === 'localhost') ||
    (appConfig?.environment === '127.0.0.1')) {
    let splitUrl = sUrl.split("/");
    sUrl = sUrl.replace("/" + splitUrl[1], appConfig?.SERVICE_BASE_URL_MAP?.[splitUrl[1]]);
  }
  return sUrl;
};

// ------------------------------------ END - UTILITY FUNCTIONS --------------------------------------------//
