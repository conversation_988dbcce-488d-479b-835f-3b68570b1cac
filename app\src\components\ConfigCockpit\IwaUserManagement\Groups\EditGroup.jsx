/* eslint-disable no-console */
import React from "react";
import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { EditGroup } from "@cw/editgroup";
import { useSnackbar } from "../../../../hooks/useSnackbar";

const EditGroupContainer = () => {
  const { groupId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSnackbar } = useSnackbar();

  const onEditGroupActionClick = (action, _groupId, response) => {
    if (action === "groupSummary") {
      navigate("/groupSummary");
    }
    if (response && response.data?.status !== "Error") {
      showSnackbar(response.message, "info");
    }
  };

  const dateTimeConfig = {
    dateFormat: "DD-MMM-YYYY",
    timeFormat: "24hr",
  };

  return (
    <>
      <EditGroup groupId={groupId} onEditGroupActionClick={onEditGroupActionClick} app="IWA" dateTimeConfig={dateTimeConfig} />
    </>
  );
};

export default EditGroupContainer;
