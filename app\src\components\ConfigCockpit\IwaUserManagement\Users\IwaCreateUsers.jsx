import { AddUser } from "@cw/adduser";
import { Stack } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useSnackbar } from "../../../../hooks/useSnackbar";

const IwaCreateUsers = () => {
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const onUserActionClick = (action, response) => {
    if (action === "usersummary") {
      navigate("configCockpit/userManagement/Users/<USER>");
    }
    if (response) {
      if (response?.status === "success" || response?.status === "SUCCESS" || response?.status === "Success") {
        showSnackbar(response.message, "info");
        return;
      }
      showSnackbar(response?.err?.data?.message, "error");
    }
  };

  return (
    <Stack>
      <AddUser onUserActionClick={onUserActionClick} />
    </Stack>
  );
};

export default IwaCreateUsers;
