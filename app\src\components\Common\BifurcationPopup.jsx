import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  IconButton,
  Paper,
  alpha,
  useTheme,
  Tooltip,
  Chip
} from "@mui/material";
import {
  DataGrid,
  gridClasses
} from "@mui/x-data-grid";
import InfoIcon from "@mui/icons-material/Info";
import CloseIcon from "@mui/icons-material/Close";
import FileIcon from "@mui/icons-material/InsertDriveFile";
import { styled } from "@mui/material/styles";
import ReusableDataTable from "./ReusableTable";
import moment from "moment/moment";
import { useSelector } from "react-redux";
import { API_CODE, LOCAL_STORAGE_KEYS, REQUEST_STATUS } from "../../constant/enum";
import { Workflow } from "@cw/rds/icons";
import DeleteForeverOutlined from "@mui/icons-material/DeleteForeverOutlined";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import { commonSearchBarUpdate } from "../../app/commonSearchBarSlice";
import { useDispatch } from "react-redux";
import { colors } from "@constant/colors";
import CustomDialog from "./ui/CustomDialog";
import { button_Outlined, button_Primary } from "../Common/commonStyles";
import { DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES } from "../../constant/enum";
import { doAjax } from "./fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import ReusableBackDrop from "./ReusableBackDrop";
import { on } from "rsuite/esm/DOMHelper";
import { END_POINTS } from "@constant/apiEndPoints";
import { setLocalStorage } from "@helper/helper";
import useLang from "@hooks/useLang";
import TruncatedText from "./ui/TruncatedText";
import SelectionSummary from "../RequestBench/SelectionSummary";

// Custom styled chip for status
const StatusChip = styled(Box)(({ theme, status }) => ({
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  height: 24,
  borderRadius: 12,
  paddingLeft: 12,
  paddingRight: 12,
  fontSize: "0.75rem",
  fontWeight: 600,
  textAlign: "center",
  backgroundColor: 
    status === "Draft" 
      ? alpha(theme.palette.error.main, 0.15)
      : status === "Pending"
        ? alpha(theme.palette.warning.main, 0.15)
        : alpha(theme.palette.success.main, 0.15),
  color: 
    status === "Draft" 
      ? theme.palette.error.dark
      : status === "Pending"
        ? theme.palette.warning.dark
        : theme.palette.success.dark
}));

const BifurcationPopup = ({ open, onClose, rowData, apiData }) => {
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({ isVisible: false, data: {} });
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [selectedRowsBifurcation, setSelectedRowsBifurcation] = useState([]);
  const appSettings = useSelector((state) => state.appSettings);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();
  const theme = useTheme();
  const transformedData = React.useMemo(() => {
    return apiData?.map(item => ({
      id: item.ChildRequestId,
      requestPrefix: item.RequestPrefix,
      requestId: item.ChildRequestId,
      requestType: item.RequestType,
      reqUpdatedOn: item.ReqUpdatedOn,
      requestStatus: item.RequestStatus,
      materials: item["Materials"].length > 0 ? `${item["Materials"]}` : "Not Available",
    })) || [];
  }, [apiData]);
  
  const handleRowClick = (params) => {
    const { requestId, requestType, requestPrefix } = params?.row;
    navigate(`/requestBench/createRequest?RequestId=${requestPrefix}${requestId}&RequestType=${requestType}&reqBench=${true}`, {
            state: {...rowData, isChildRequest: true}
    });
  };

  const columns = [
    { 
      field: "requestId", 
      headerName: t("Child Request ID"), 
      flex: 1,
      align: "center",
      minWidth: 150,
      headerAlign: "center",
      renderCell: (params) => {
        return <Typography sx={{ fontSize: "12px" }}>{params.row.requestPrefix}{params.row.requestId}</Typography>;
      }
    },
    { 
      field: "requestType", 
      headerName: t("Request Type"), 
      flex: 1,
      
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return <Typography sx={{ fontSize: "12px" }}>{params.row.requestType}</Typography>;
      }
    },
    { 
      field: "materials", 
      headerName: t("Materials"), 
      flex: 1,
      maxWidth: 130,
      headerAlign: "center",
      renderCell: (params) => {
        const materials = params.value ? params.value.split(",").map(m => m.trim()) : [];
        const displayCount = materials.length - 1;

        if (materials.length === 0) return "-";

        return (
          <Box sx={{ 
            display: "flex", 
            alignItems: "center",
            width: "100%",
            minWidth: 0 
          }}>
            <Tooltip 
              title={materials[0]}
              placement="top"
              arrow
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  minWidth: 0,
                }}
              >
                {materials[0]}
              </Typography>
            </Tooltip>
            {displayCount > 0 && (
              <Box sx={{ 
                display: "flex",
                alignItems: "center",
                ml: 1,
                flexShrink: 0 
              }}>
                <Tooltip
                  arrow
                  placement="right"
                  title={
                    <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        {t("Additional Materials")} ({displayCount})
                      </Typography>
                      {materials.slice(1).map((material, idx) => (
                        <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                          {material}
                        </Typography>
                      ))}
                    </Box>
                  }
                >
                  <Box sx={{ 
                    display: "flex", 
                    alignItems: "center",
                    cursor: "pointer"
                  }}>
                    <InfoIcon 
                      sx={{ 
                        fontSize: "1rem",
                        color: "#3b30c8",
                        "&:hover": { color: "#2e25a0" }
                      }} 
                    />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        ml: 0.5,
                        color: "#3b30c8",
                        fontSize: "11px"
                      }}
                    >
                      +{displayCount}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      },
    },
    { 
      field: "reqUpdatedOn", 
      headerName: t("Updated On"), 
      flex: 1,
      minWidth: 120,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return <Typography sx={{ fontSize: "12px" }}>{moment(params.row.reqUpdatedOn).format(appSettings?.dateFormat)}</Typography>;
      }
    },
    { 
      field: "requestStatus", 
      headerName: t("Status"), 
      flex: 0.8,
      minWidth: 150,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <StatusChip status={params.row.requestStatus}>
          <TruncatedText text={params.row.requestStatus} maxChars={17}/>
        </StatusChip>
      )
    },
    {
          field: "actions",
          align: "center",
          flex: 1.4, 
          headerAlign: "center",
          headerName: t("Actions"),
          sortable: false,
          renderCell: (params) => {
            return (
            <div>
              <Tooltip title="View Flow">
                <IconButton
                  disabled={params?.row?.requestStatus === `${REQUEST_STATUS?.DRAFT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.UPLOAD_SUCCESSFUL}` || params?.row?.requestStatus === `${REQUEST_STATUS?.UPLOAD_FAILED}` || params?.row?.requestStatus === `${REQUEST_STATUS?.VALIDATED_REQUESTOR}` || params?.row?.requestStatus === `${REQUEST_STATUS?.VALIDATION_FAILED_REQUESTOR}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATION_FAILED_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}`}
                  aria-label="View Metadata"
                  onClick={() => {
                    setLocalStorage(LOCAL_STORAGE_KEYS.REQUEST_BENCH_TASK, params?.row);
                    dispatch(
                      commonSearchBarUpdate({
                        module: "RequestHistory",
                        filterData: {
                          reqId: params.row.requestPrefix + params.row.requestId,
                        },
                      })
                    );
                    onClose();
                    navigate("/requestBench/RequestHistory",{state:rowData?.requestId});
                  }}
                >
                  <Workflow
                    color={
                      params?.row?.requestStatus === `${REQUEST_STATUS.DRAFT}` || params?.row?.requestStatus === `${REQUEST_STATUS.UPLOAD_SUCCESSFUL}` || params?.row?.requestStatus === `${REQUEST_STATUS.UPLOAD_FAILED}` || params?.row?.requestStatus === `${REQUEST_STATUS.VALIDATED_REQUESTOR}` || params?.row?.requestStatus === `${REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATION_FAILED_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}`
                        ? "#808080"
                        : "#3B30C8"
                    }
                  />
                </IconButton>
              </Tooltip>
    
              <Tooltip title={t("Cancel")}>
                <IconButton
                  disabled={params?.row?.requestStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP}` || params?.row?.requestStatus === `${REQUEST_STATUS.CANCELED}` || params?.row?.requestStatus === `${REQUEST_STATUS.REJECTED}` || params?.row?.requestStatus === `${REQUEST_STATUS.UPLOAD_FAILED}` || params?.row?.requestStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY}`}
                  aria-label="View Metadata"
                  onClick={() => {
                    setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
                  }}
                >
                  <DeleteForeverOutlined
                    sx={{
                      color: (theme) => (params?.row?.requestStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP}` || params?.row?.requestStatus === `${REQUEST_STATUS.CANCELED}` || params?.row?.requestStatus === `${REQUEST_STATUS.REJECTED}` || params?.row?.requestStatus === `${REQUEST_STATUS.UPLOAD_FAILED}` || params?.row?.requestStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}` || params?.row?.requestStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY}` ? "#808080" : "#cc3300"),
                    }}
                  />
                </IconButton>
              </Tooltip>
              <Tooltip title={t("Error Report")}>
                <IconButton
                  disabled={params?.row?.requestStatus === `${REQUEST_STATUS.DRAFT}` || params?.row?.requestStatus === `${REQUEST_STATUS.UPLOAD_SUCCESSFUL}`}
                  onClick={() => {
                    // handleErrorReport(params.row.requestId, params.row.requestType);
                    onClose();
                    navigate(`/requestBench/errorHistory?RequestId=${params?.row?.requestPrefix}${params?.row?.requestId}`,{ state: { childRequest: true } });
                  }}
                >
                  <SummarizeOutlinedIcon
                    sx={{
                      color: params?.row?.requestStatus === `${REQUEST_STATUS.DRAFT}` || params?.row?.requestStatus === `${REQUEST_STATUS.UPLOAD_SUCCESSFUL}` ? "#808080" : "#ffd93f",
                    }}
                  />
                </IconButton>
              </Tooltip>
            </div>
          )},
        },
  ];

  const handleDelete = () => {
    handleCancel(isDeleteDialogVisible?.data.row.requestId, isDeleteDialogVisible?.data.row.requestType);
  };

  const handleCancel = (requestId, requestType, cb) => {
    setLoaderMessage("Request processing please wait.");
    setBlurLoading(true);
    let cancelPayload = {
      requestId: requestId,
      requestType: requestType,
    };

    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (data.statusCode === API_CODE.STATUS_200) {
          onClose();
      }
      setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
      if (cb) cb();
    };
    const hError = () => {
      setLoaderMessage("");
      setBlurLoading(false);
      setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
      if (cb) cb();
    };

    doAjax(`/${destination_MaterialMgmt}${END_POINTS.WORK_FLOW.CANCEL_WORKFLOW}`, "post", hSuccess, hError, cancelPayload);
  };


  const isRowSelectable = (params) => {
    const status = params.row.requestStatus || params.row.reqStatus;
    return !(
      status === REQUEST_STATUS.REJECTED ||
      status === REQUEST_STATUS.SYNDICATED_IN_SAP ||
      status === REQUEST_STATUS.CANCELED ||
      status === REQUEST_STATUS.UPLOAD_FAILED ||
      status === REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT ||
      status === REQUEST_STATUS.SYNDICATED_PARTIALLY_DIRECT ||
      status === REQUEST_STATUS.SYNDICATED_PARTIALLY
    );
  };

  const handleMassCancelBifurcation = async () => {
    if (selectedRowsBifurcation.length === 0) return;
    setBlurLoading(true);
    setLoaderMessage("Request processing please wait.");
    let cancelCount = 0;
    for (const id of selectedRowsBifurcation) {
      const row = transformedData.find((r) => r.id === id);
      if (!row) continue;
      await new Promise((resolve) => {
        handleCancel(row.requestId, row.requestType, resolve);
      });
      cancelCount++;
    }
    setBlurLoading(false);
    setLoaderMessage("");
    setSelectedRowsBifurcation([]);
  };

  return (
    <>
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{ 
        sx: { 
          borderRadius: 2,
          overflow: "hidden",
        } 
      }}
    >
      <DialogTitle 
        sx={{ 
          display: "flex", 
          justifyContent: "space-between", 
          alignItems: "center",
          p: 2.5,
          backgroundColor: theme.palette.background.default
        }}
      >
        <Typography variant="h5" fontWeight="bold" color="text.primary">
          {t("Request Details")}
        </Typography>
        <IconButton onClick={onClose} size="small" aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* <Alert 
          severity="info" 
          icon={<InfoIcon />}
          sx={{ 
            mb: 3,
            borderRadius: 1.5,
            "& .MuiAlert-message": {
              fontWeight: 500
            }
          }}
        >
          This request is bifurcated and cannot be opened directly. Please select one of the requests below.
        </Alert> */}

          <ReusableDataTable
            rows={transformedData || []}
            getRowIdValue={"requestId"}
            columns={columns}
            tempheight={"50vh"}
            pageSize={5}
            rowsPerPageOptions={[5, 10, 25]}
            disableSelectionOnClick
            status_onRowDoubleClick={true}
            callback_onRowDoubleClick={handleRowClick}
            checkboxSelection={true}
            rowHeight={40}
            selectionModel={selectedRowsBifurcation}
            onRowsSelectionHandler={setSelectedRowsBifurcation}
            isRowSelectable={isRowSelectable}
          />

      </DialogContent>

      <DialogActions sx={{ p: 2.5, backgroundColor: theme.palette.background.default }}>
        <Button
          variant="contained"
          size="large"
          onClick={onClose}
          sx={{
            px: 4,
            borderRadius: 1.5,
            textTransform: "none",
            fontWeight: 600
          }}
        >
          {t("Close")}
        </Button>
      </DialogActions>
    </Dialog>
    {isDeleteDialogVisible?.isVisible && (
        <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteForeverOutlined size="small" color="error" sx={{ fontSize: "20px" }} />} Title={"Cancel Request!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
          <DialogContent sx={{ mt: 2 }}>{t(DIALOUGE_BOX_MESSAGES.CANCEL_MESSAGE)}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
              {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleDelete}>
              {t(DELETE_MODAL_BUTTONS_NAME.DELETE)}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
      <Box sx={{ position: 'relative', width: '100%', mt: 2, mb: 2 }}>
        <SelectionSummary
          selectedRows={selectedRowsBifurcation}
          count={transformedData.length}
          tableData={transformedData}
          handleMassCancel={handleMassCancelBifurcation}
        />
      </Box>
    </>
  );
};

export default BifurcationPopup;
