import { RoleSummary } from '@cw/rolesummary';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const IwaRolesSummary = () => {
  const navigate = useNavigate();
  const [, setIsRoleEditable] = useState(false);
  const isVisible = {
    isCreateRoleVisible: true,
    isCopyCreateWithReferenceVisible: true,
    isDeleteVisible: true,
    isInActiveAndActiveVisible: true,
    isExportVisible: true,
    isSimpleRoleVisible: true,
    isModuleFeatureRoleVisible: true,
  };
     const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };


  const onRoleSummaryActionClick = (action, data) => {
    switch (action) {
      case 'createRole':
        navigate('/configCockpit/userManagement/CreateRole', {
          state: data
            ? {
                mapGroups: data?.mapGroups ?? [],
                mapRoleCollections: data?.mapRoleCollections ?? [],
              }
            : undefined,
        });
        break;

      case 'viewRole':
      case 'editRole':
        if (data?.roleId && data?.roleVersionNo && data?.roleSegment) {
          setIsRoleEditable(action === 'editRole');
          const path = `/configCockpit/userManagement/ViewAndEditRole`;
          navigate(path, {
            state: {
              status: data?.status,
              action,
              roleId: data?.roleId,
              roleVersionNo: data?.roleVersionNo,
              roleSegment: data?.roleSegment,
            },
          });
        }

        break;

      default:
        console.warn('Unhandled action type:', action);
    }
  };

  return <RoleSummary isVisible={isVisible} onRoleSummaryActionClick={onRoleSummaryActionClick} dateTimeConfig={dateTimeConfig} />;
};

export default IwaRolesSummary;
