import { EditUser } from "@cw/edituser";
import { useNavigate, useParams } from "react-router-dom";
import { useSnackbar } from "../../../../hooks/useSnackbar";

const IwaEditUser = () => {
  const { showSnackbar } = useSnackbar();
  const { userId } = useParams();
  const navigate = useNavigate();

  const editUserNavigate = (action, userId, response) => {
    if (action === "home") {
      navigate("/configCockpit/userManagement/UsersSummary");
    }
    if (response) {
      if (response?.status === "success" || response?.status === "SUCCESS" || response?.status === "Success") {
        showSnackbar(response.message, "info");
        return;
      }
      showSnackbar(response?.err?.data?.message, "error");
    }
  };

  return (
    <div>
      <EditUser userId={userId} editUserNavigate={editUserNavigate} />
    </div>
  );
};

export default IwaEditUser;
