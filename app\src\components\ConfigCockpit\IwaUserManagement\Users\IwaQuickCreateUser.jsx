import { QuickAddUser } from '@cw/quickadduser';
import { Stack } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from "../../../../hooks/useSnackbar";

const IwaQuickCreateUser = () => {
   const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const onUserActionClick = (action, response) => {
    if (action === 'home' || action === 'usersummary') {
      navigate('/configCockpit/userManagement/UsersSummary');
    }

    if (response) {
      showSnackbar(response.message, "info");
    }
  };
  return (
    <Stack>
      <QuickAddUser onUserActionClick={onUserActionClick} />
    </Stack>
  );
};

export default IwaQuickCreateUser;
