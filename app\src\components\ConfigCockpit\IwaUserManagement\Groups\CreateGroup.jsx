/* eslint-disable no-console */
import React from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useSnackbar } from "../../../../hooks/useSnackbar";
import { CreateGroup } from "@cw/creategroup";

const CreateGroupContainer = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSnackbar } = useSnackbar();

  const onCreateGroupActionClick = (action, response) => {
    if (action === "groupSummary") {
      navigate("/groupSummary");
    }
    if (response) {
    showSnackbar(response.message ,"info" );
    }
  };

  const dateTimeConfig = {
    dateFormat: "DD-MMM-YYYY",
    timeFormat: "24hr",
  };

  return (
    <>
      <CreateGroup onCreateGroupActionClick={onCreateGroupActionClick} dateTimeConfig={dateTimeConfig} />
    </>
  );
};

export default CreateGroupContainer;
