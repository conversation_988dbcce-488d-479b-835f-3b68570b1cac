import { MFViewandEdit } from '@cw/mfviewandedit';
import { SimpleViewAndEdit } from '@cw/viewandeditrole';
import { Stack } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';

const IwaViewAndEditRole = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { roleId, roleVersionNo, action, roleSegment, status } = location.state;
  const isEditMode = action === 'editRole' ? true : false;
  const simpleViewAndEditProps = {
    isEditMode: isEditMode,
    roleVersionNo,
    roleId,
    status,
  };

  const onSimpleViewAndEditClick = action => {
    if (action === 'roleSummary') {
      navigate('/configCockpit/userManagement/RolesSummary');
    }
    if (action === 'editRole') {
      navigate(`/configCockpit/userManagement/ViewAndEditRole`, {
        state: { roleId, roleVersionNo, action, roleSegment, status },
      });
    }
  };

  const MFViewandEditProps = {
    isEdit: isEditMode,
    roleId: roleId,
    roleVersionNo: roleVersionNo,
    status,
  };

  const onMFViewandEditActionClick = action => {
    if (action === 'roleSummary') {
      navigate('/configCockpit/userManagement/RolesSummary');
    }
    if (action === 'editRole') {
      navigate(`/configCockpit/userManagement/ViewAndEditRole`, {
        state: { roleId, roleVersionNo, action, roleSegment, status },
      });
    }
  };

  return (
    <Stack>
      {roleSegment?.includes('Simple') ? (
        <SimpleViewAndEdit
          simpleViewAndEditProps={simpleViewAndEditProps}
          onSimpleViewAndEditClick={onSimpleViewAndEditClick}
        />
      ) : (
        <MFViewandEdit
          MFViewandEditProps={MFViewandEditProps}
          onMFViewandEditActionClick={onMFViewandEditActionClick}
        />
      )}
    </Stack>
  );
};

export default IwaViewAndEditRole;
